# Security Policy

## Supported Versions

| Version | Supported          |
| ------- | ------------------ |
| 16.0    | :white_check_mark: |
| 15.0    | :white_check_mark: |
| 14.0    | :white_check_mark: |
| <=13.0  | :x:                |

## Reporting a Vulnerability

Please share privately the details of your security vulnerability by contacting our Security Team:
[Contact Info](https://www.odoo.com/security-report)

Make sure to include as much information as possible, with the detailed steps to reproduce the problem,
the versions that are affected, the expected results and actual results, and any other information that
might help us react faster and more efficiently.

We tend to prefer _text-based descriptions_ accompanied with a proof-of-concept script/exploit, rather
than screenshots and videos.

Our [Responsible Disclosure](https://www.odoo.com/security-report) page gives an overview of the
process, including:

 - Our Incident Response Procedure (what will happen after you report an issue)
 - Our Rules (what you can and cannot do while researching security issues)
 - Guidelines with **DO REPORT** and **DO NOT REPORT** issues
   (what kind of issues will be accepted/rejected)


## Important note

We receive a majority of security reports that have little to no impact on the security of Odoo or
the Odoo Cloud, and we ultimately have to reject them. To avoid a disappointing experience when
contacting us, please try to put together a proof-of-concept attack and take a critical look at
what's really at risk.
If the proposed attack scenario turns out unrealistic, your report will probably be rejected.
Also be sure to review our list of [non-qualifying issues](https://www.odoo.com/security-report#what).
