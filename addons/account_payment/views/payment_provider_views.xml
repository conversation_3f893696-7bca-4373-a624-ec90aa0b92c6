<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">payment.provider.form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="payment_followup" position="attributes">
                <attribute name="invisible">
                    0
                </attribute>
            </group>
            <group name="payment_followup" position="inside">
                <field name="journal_id"
                    context="{'default_type': 'bank'}"
                    attrs="{'required': [('state', '!=', 'disabled'), ('code', 'not in', ['none', 'custom'])]}"/>
            </group>
        </field>
    </record>

</odoo>
