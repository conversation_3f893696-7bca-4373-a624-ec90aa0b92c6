# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Direct subordinates"
msgstr ""

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "कर्मचारी"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Indirect subordinates"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "More managers"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "No hierarchy position."
msgstr ""

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr ""

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Redirect"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "See All"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hooks.js:0
#, python-format
msgid "Team"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "This employee has no manager or subordinate."
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Total"
msgstr ""
