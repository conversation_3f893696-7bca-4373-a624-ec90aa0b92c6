# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "기본 직원"

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "직속 및 관련 부하 직원"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Direct subordinates"
msgstr "직속 부하 직원"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "임직원"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr "조직도를 가져 오려면 관리자를 설정하고 기록을 저장하십시오."

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "전체 부하 직원 수"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Indirect subordinates"
msgstr "관련 부하 직원"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "More managers"
msgstr "관리자 더보기"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "No hierarchy position."
msgstr "계층 구조가 없습니다."

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "조직도"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "일반 직원"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Redirect"
msgstr "리디렉션"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "See All"
msgstr "모두 보기"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "부하 직원"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hooks.js:0
#, python-format
msgid "Team"
msgstr "팀"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "This employee has no manager or subordinate."
msgstr "이 직원에게는 관리자 또는 부하 직원이 없습니다."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Total"
msgstr "합계"
