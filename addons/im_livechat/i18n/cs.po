# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <jan.ho<PERSON><PERSON><PERSON>@centrum.cz>, 2022
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# Tereza <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Marta Wacławek, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#, python-format
msgid " (copy)"
msgstr "(kopírovat)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "\"%s\" is not a valid email."
msgstr "\"%s\" není platný email."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr "Počet zpráv"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "# Hodnocení"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "počet sezení"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "počet mluvčích"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% spokojeno"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% Štěstí"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s a %s právě píše..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s has joined"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s píše..."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s a další píší..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr ""
"'%(input_email)s' nezdá se být platným emailem. Můžete to zkusit znovu?"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", na"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Zobrazit starší zprávy --------"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr "1 je pondělí 7 je neděle"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Back to edit mode"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Procento spokojených "
"hodnocení\" role=\"img\" aria-label=\"Veselá tvář\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr "<i class=\"fa fa-user\" role=\"img\" aria-label=\"Uživatel\" title=\"Uživatel\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">Živý chat konverzace</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>S pozdravem,</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>Dobrý den,</span><br/>zde je kopie vaší diskuse s "

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr ""
"<span>Upomínka: Tento krok bude proveden pouze v případě, že žádný operátor "
"není dostupný.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""
"<span>Tip: Aspoň jedna interakce (otázka, e-mail, ...) je vyžadována předtím, než bude bot schopen provést pokročilejší akce (přesměrování k operátorovi, ...). </span>\n"
"                    <span>Použijte pravidla kanálu, pokud chcete, aby bot interagoval s návštěvníky pouze tehdy, když žádný operátor není k dispozici.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr ""
"<span>Tip: Aspoň jedna interakce (otázka, e-mail, ...) je vyžadována "
"předtím, než bude bot schopen provést pokročilejší akce (přesměrování k "
"operátorovi, ...).</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr ""
"<span>Tip: Naplánujte další kroky pro bota, v případě že žádný operátor není"
" k dispozici.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr "<span>Právě testujete</span>"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr "mail.message může být propojeno pouze k jedné zprávě chatbota"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"Relace je bez odpovědi, pokud operátor neodpověděl. \n"
"                                       Pokud je návštěvník také operátorem, relace bude mít vždy odpověď."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "Aktivní"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr "A tadaaaa tady to máte! 🌟"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "Anonymní"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Anonymní jméno"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "Odpovědět"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "Odpovědi"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Jste uvnitř matice?"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Ask something ..."
msgstr "Zeptejte se na něco..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "Účastníci"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Average Rating"
msgstr "Průměrné hodnocení"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Průměrné hodnocení (%)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Průměrná doba trvání"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Průměrná zpráva"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr ""
"Průměrná doba v sekundách, po kterou má návštěvník poskytnout první odpověď"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Průměrná doba pro poskytnutí první odpovědi návštěvníkovi"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Bad"
msgstr "Špatný"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr "Robotický operátor"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Barva pozadí tlačítka"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Barva textu tlačítka"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Předpřipravené odpovědi"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Konzervované odpovědi umožňují vkládat do zpráv předem napsané odpovědi "
"zadáním <i>:shortcut</i>. Zkratka se nahradí přímo ve zprávě, takže ji "
"můžete před odesláním ještě upravit."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr "Kanál"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "Barva záhlaví kanálu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Název kanálu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Pravidlo kanálu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Pravidla kanálu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Typ kanálu"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "Kanály"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Vlastník vstupu chatu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Chat je soukromý a jedinečný mezi dvěma osobami. Skupina je soukromá mezi "
"pozvanými osobami. Kanál může být libovolně zpřístupněn (v závislosti na "
"jeho konfiguraci)."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr "Chatujte s jedním z našich spolupracovníků"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr "Chatbot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr "Aktuální krok chatbota"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr "Chatbot zpráva"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr "Chatbot zprávy"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr "Název chatbota"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Chatbot skript"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr "Chatbot odpověď skriptu"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Chatbot krok skriptu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr "Krok chatbotu"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr "Chatboti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "Zavřít"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Close conversation"
msgstr "Úzká konverzace"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Kód"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Konfigurace"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Konverzace"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Conversation Sent"
msgstr "Konverzace odeslána"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "Conversation ended..."
msgstr ""

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Konverzace s %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "Zpracované konverzace"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Tento kód zkopírujte a vložte na své webové stránky mezi tagy &lt;head&gt;"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr "Stát"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "Země návštěvníka"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "Země návštěvníka kanálu"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr "Vytvořit chatbot"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "Vytvořte kanál a začněte živý chat, abyste vyplnili svoji historii."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr "Vytvořte novou konzervovanou odpověď"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr "Datum vytvoření"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Datum vytvoření"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Čas vytvoření (hodina)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Hodnocení zákazníků"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Číslo dne"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Dny aktivity"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Výchozí barva pozadí tlačítka živého chatu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "Výchozí barva pozadí záhlaví kanálu po otevření "

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Výchozí barva textu tlačítka živého chatu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Výchozí text je zobrazen na tlačítku živý chat podpora"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "Výchozí barva názvu kanálu po otevření"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Definujte nový kanál živého chatu webových stránek"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""
"Definujte pravidla pro kanál živé podpory. Můžete použit akci pro danou URL "
"a pro zemi.<br/>Pro identifikaci země, na musí být vašem serveru  "
"nainstalován GeoIP, jinak nebudou země v pravidle zohledněny."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr ""
"Zpoždění (v sekundách) kdy se automaticky otevře okno konverzace. Poznámka: "
"zvolená akce musí být \"Otevřít automatický\", jinak se tento parametr "
"nebude brát v úvahu."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "Odpověděli jsme správně na Vaši otázku?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Přehled"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_channel_id
msgid "Discussion Channel"
msgstr "Diskusní kanál"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Doba konverzace (v sekundách)"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "E-mail "

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Explain your note"
msgstr "Vysvětlete svou poznámku"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr "Nesprávný první krok"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr "Operátor prvního kroku"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr "Varování prvního kroku"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"U webů vytvořených pomocí Odoo CMS přejděte na Web &gt; Konfigurace &gt; "
"Nastavení a vyberte kanál živého chatu na webu, který chcete přidat na svůj "
"web."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr "Přeposlat operátorovi"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr "Pole pro zadávání libovolného textu"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr "Pole pro zadávání libovolného textu (Více řádků)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Určete pořadí nalezení odpovídajícího pravidla. Pokud se pro danou adresu "
"URL / zemi shodují 2 pravidla, bude vybráno pravidlo s nejnižší "
"posloupností."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Good"
msgstr "Dobrý"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "Seskupit podle…"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#, python-format
msgid "Have a Question? Chat with us."
msgstr "Máte otázku? Napište nám na chatu."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "Barva pozadí záhlaví"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "Skrýt"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "Historie"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr ""
"Hmmm, dovolte mi zkontrolovat, zda je někdo, kdo by vám s tím mohl pomoci..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Hodina zahájení datum relace"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "How may I help you?"
msgstr "Jak vám mohu pomoci?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "Jak používat widget živý chat na webu?"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "Jejda, vypadá to, že žádný z naších operátorů není k dispozici 🙁"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr "Jen se rozhlížím"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr "Hledám vaši dokumentaci"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr "Mám dotaz ohledně ceny"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr "Pokud potřebujete ještě něco, neváhejte nás znovu kontaktovat."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "Obrázek"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "Obrázek 1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "Obrázek 128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "Obrázek 256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "Obrázek 512"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.js:0
#, python-format
msgid "Invalid email address"
msgstr "Neplatná emailová adresa"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "Probíhá živý chat?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "Návštěvník je anonymní"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Připojit"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Připojit se ke kanálu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "Hodnota kpi konverzací živého chatu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "Hodnota kpi hodnocení živého chatu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "Hodnota kpi odpovědi živého chatu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Posledních 24 hodin"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message____last_update
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script____last_update
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer____last_update
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Uvolnit"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Opustit kanál"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_member
msgid "Listeners of a Channel"
msgstr "Posluchači kanálu"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Živý chat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr "Tlačítko živého chatu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "Vyhledávání kanálů živého chatu"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/discuss_sidebar_category.js:0
#: code:addons/im_livechat/static/src/models/mobile_messaging_navbar_view.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Živý chat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Tlačítko živého chatu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Barva tlačítka živého chatu"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Živý chat kanál"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr "Počet kanálu živého chatu"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Pravidla kanálu živého chatu"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Konverzace v živém chatu"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "Pro kanál typu živý chat se vyžaduje ID operátora živého chatu."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Zpráva o podporovaném kanálu živého chatu"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr ""
"Statistiky kanálu podpory živého chatu vám umožňují snadno analyzovat výkon "
"konverzaci živého chatu ve vaší společnosti. Získávejte informace o "
"zmeškaných konverzacích, publiku, délce relaci atd."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Zpráva operátora živého chatu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Statistiky podpory živý chat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr "Živý chat jméno"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Okno živého chatu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "Načítání starších zpráv..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Odpovídající objednávka"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "Zpráva"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Zmeškané relace"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "Jméno"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "New messages"
msgstr "Nové zprávy"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "Žádný dostupný spolupracovník, zkuste to prosím později."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "Zatím není žádné hodnocení zákazníků v živém chatu "

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Zatím žádná data!"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr "Nenalezena žádná historie"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_window/public_livechat_window.xml:0
#, python-format
msgid "No operator available"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Note by"
msgstr "Poznámka od"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr "Číslo chatbotu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "Počet konverzací"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Počet dní od prvního sezení operátora"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Počet různých mluvčích"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Počet zpráv v konverzaci"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Jméno na online chatu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr "Pouze když"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr "Jejda! Něco se pokazilo."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr "Otevřít automaticky"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr "Otevřít časovač automaticky"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operátor"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Analýza operátorů"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operátoři"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Operátoři, kteří nevykazují žádnou aktivitu v Odoo po dobu delší než 30 "
"minut, budou považováni za odpojené."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr "Volitelný odkaz"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Možnosti"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Procento šťastných hodnocení"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "Telefon"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please call me on: "
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr "Zkontrolujte prosím připojení k internetu."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please contact me on: "
msgstr ""

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr ""
"Prosím, neváhejte! Pokud vám můžeme s čímkoli pomoci, dejte nám vědět."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Please wait"
msgstr "Prosím čekejte"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Please wait..."
msgstr "Prosím, čekejte..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Běží na"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "Otázka"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Rating"
msgstr "Hodnocení"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "Hodnocení prům. textu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Hodnocení Poslední připomínky"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Hodnocení Posledního obrázku"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Hodnocení Poslední hodnoty"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Hodnocení spokojenosti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_text
msgid "Rating Text"
msgstr "Text hodnocení"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "Počet hodnocení"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.js:0
#, python-format
msgid "Rating: %s"
msgstr "Hodnocení: %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr "Hodnocení"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Hodnocení kanálu živý chat"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr "Získejte kopii této konzervace"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr "Přesměrování"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Regulární výraz určující webové stránky, na které se bude vztahovat toto "
"pravidlo."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr "Související emailová zpráva"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Výkazy"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#, python-format
msgid "Reset to default colors"
msgstr "Obnovit výchozí barvy"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "Restart"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "Restart Conversation"
msgstr "Restartujte konverzaci"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Restarting conversation..."
msgstr "Restartování konverzaci..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Pravidla"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Míra spokojenosti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Uložte si kanál a získejte konfigurační widget."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/public_livechat_window.js:0
#, python-format
msgid "Say something"
msgstr "Řekněte něco"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "Skript"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Scénář (externí)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr "Krok skriptu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr "Kroky skriptu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "Historie hledání"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "zpráva o hledání"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/models/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Viz 15 posledních navštívených stránek"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/public_livechat_window.js:0
#, python-format
msgid "Select an option above"
msgstr "Vyberte možnost výše"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Send"
msgstr "Odchozí"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "Datum Sezení"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "Formulář sezení"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Statistiky relací"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr "Relace vypršela... Obnovte prosím a zkuste to znovu."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Relace není hodnocena"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Relace bez odpovědi"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Sezení"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Historie relací"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "Zobrazit"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr ""
"Zobrazit tento krok, pouze v případě, že byly vybrány všechny tyto odpovědi."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr "Zobrazit s notifikaci"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "Zdroj"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Datum začátku relace"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Začátek hodiny relace"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Typ kroku"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "Text"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Text tlačítka"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Text, který vyzve uživatele na začátku chatu."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Děkujeme vám za vaši reakci"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Kanál tohoto pravidla"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""
"Toto pravidlo se bude vztahovat pouze na tyto země. Příklad: pokud vyberete "
"\"Belgie\" a \"Spojené státy\" a nastavíte akci na \"Skrýt\", tlačítko chatu"
" bude skryto na zadané URL adrese pro návštěvníky z těchto dvou zemí. Tato "
"funkce vyžaduje instalaci GeoIP na vašem serveru."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Uživatel bude moci odstranit kanály podpory."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Uživatel se bude moci připojit ke kanálům podpory."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr ""
"Po kliknutí na tuto možnost bude návštěvník přesměrován na tento odkaz "
"(pozor, skript se ukončí, pokud je odkaz mimo webovou stránku živého chatu)."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "Momentálně není žádné hodnocení pro tento kanál."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Tento týden"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Jedná se o automatickou \"uvítací\" zprávu, kterou si návštěvník uvidí při "
"zahájení nové konverzace."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""
"Toto uživatelské jméno bude použito jako vaše jméno v kanálech živého chatu."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Čas na odpověď"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "Čas na odpověď (sek.)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "Tip: Pomocí předem připravených odpovědí můžete chatovat rychleji."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "Název"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "Barva nadpisu"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat_message.js:0
#, python-format
msgid "Today"
msgstr "Dnes"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Ošetřené relace"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Try again"
msgstr "Zkuste to znovu"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"Adresa URL na statickou stránku, kde může klient diskutovat s operátorem "
"kanálu."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Uživatel"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "Uživatelské jméno uživatele živého chatu"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Uživatelské nastavení"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr "Odpověď uživatele"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr "Neupravená odpověď uživatele"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Visitor"
msgstr "Návštěvník"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr "Návštěvník opustil konverzaci."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "Návštěvník je šťastný"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Webová strana"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Kanály živého chatu webové stránky"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr "Uvítací bot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Uvítací zpráva"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName ! 👋"
msgstr ""

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr "Co hledáte?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Widget"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""
"Mohli byste zanechat svou emailovou adresy, abychom vás mohli kontaktovat "
"zpět?"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat_message.js:0
#, python-format
msgid "Yesterday"
msgstr "Včera"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "Vy"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr ""
"Můžete vytvořit nového chatbota s definovaným skriptem, který bude mluvit s "
"návštěvníky vašeho webu."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"Pro každou webovou stránku, na kterou chcete integrovat widget živého chatu,"
" můžete vytvořit kanály, které návštěvníkům vašeho webu umožní mluvit v "
"reálném čase s vašimi operátory."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr "Vaše historie chatování je prázdná"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "chatbot_image"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr "např. \"Robot plánovač schůzek\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr "Např. „Jak vám mohu pomoci?\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "např. /kontaktujtenas"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "např. Dobrý den, jak Vám mohu pomoci?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "např. vasestranka.cz"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "is typing"
msgstr ""

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"nebo zkopírujte tuto adresu URL a odešlete ji e-mailem svým zákazníkům nebo "
"dodavatelům:"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.js:0
#, python-format
msgid "read less"
msgstr "číst méně"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.js:0
#, python-format
msgid "read more"
msgstr "číst více"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "vteřin"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
