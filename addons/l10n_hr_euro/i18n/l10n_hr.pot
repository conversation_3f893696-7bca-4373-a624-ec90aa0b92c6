# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_hr
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-12 09:46+0000\n"
"PO-Revision-Date: 2022-08-12 09:46+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_O
#: model:account.tax,description:l10n_hr.2_VAT_P_O
#: model:account.tax.template,description:l10n_hr.VAT_P_O
msgid "0% Domestic supplies"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_EU_G
#: model:account.tax,name:l10n_hr.2_VAT_S_EU_G
#: model:account.tax.template,name:l10n_hr.VAT_S_EU_G
msgid "0% EU G"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_EU_S
#: model:account.tax,name:l10n_hr.2_VAT_S_EU_S
#: model:account.tax.template,name:l10n_hr.VAT_S_EU_S
msgid "0% EU S"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_EX_O
#: model:account.tax,name:l10n_hr.2_VAT_S_EX_O
#: model:account.tax.template,name:l10n_hr.VAT_S_EX_O
msgid "0% EX"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_install_assemb_goods_O
#: model:account.tax,name:l10n_hr.2_VAT_P_install_assemb_goods_O
#: model:account.tax.template,name:l10n_hr.VAT_P_install_assemb_goods_O
msgid "0% IAG other state"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_new_transport_other_state_O
#: model:account.tax,name:l10n_hr.2_VAT_S_new_transport_other_state_O
#: model:account.tax.template,name:l10n_hr.VAT_S_new_transport_other_state_O
msgid "0% M T other state"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_person_not_in_ROC_O
#: model:account.tax,name:l10n_hr.2_VAT_S_person_not_in_ROC_O
#: model:account.tax.template,name:l10n_hr.VAT_S_person_not_in_ROC_O
msgid "0% P not in ROC"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_carried_other_state_O
#: model:account.tax,name:l10n_hr.2_VAT_S_carried_other_state_O
#: model:account.tax.template,name:l10n_hr.VAT_S_carried_other_state_O
msgid "0% S C other state"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_install_assemb_goods_O
#: model:account.tax,description:l10n_hr.2_VAT_P_install_assemb_goods_O
#: model:account.tax.template,description:l10n_hr.VAT_P_install_assemb_goods_O
msgid "0% installed and assembled goods in another state"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_other_exempt_O
#: model:account.tax,name:l10n_hr.2_VAT_S_other_exempt_O
#: model:account.tax.template,name:l10n_hr.VAT_S_other_exempt_O
msgid "0% other exempt"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_reverse_O
#: model:account.tax,name:l10n_hr.2_VAT_S_reverse_O
#: model:account.tax.template,name:l10n_hr.VAT_S_reverse_O
msgid "0% reverse"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_V_1_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_V_1
msgid ""
"1. Claims for the difference of higher input tax than liabilities in the "
"taxation period"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_1_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_1_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_1
msgid "1. Input vat related to supplies received in the country at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_1_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_1_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_1
msgid ""
"1. Supplies in the republic of croatia in respect of which the receiver "
"calculates vat (domestic reverse charge)"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_1_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_1_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_1
msgid ""
"1. Supplies of goods and services in the republic of croatia at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_IV_1_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_IV_1
msgid "1. To pay"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_VI_1_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_VI_1
msgid "1. Total to pay"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_10_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_10_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_10
msgid "10. Input vat related to services received from eu at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_10_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_10_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_10
msgid "10. Other exemptions"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_10_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_10_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_10
msgid "10. Services received from eu at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_11_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_11_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_11
msgid ""
"11. Input vat related to supplies of goods and services received from non-"
"established taxable persons at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_11_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_11_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_11
msgid ""
"11. Supplies of goods and services received from taxable persons not "
"established in the republic of croatia at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_12_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_12_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_12
msgid ""
"12. Input vat related to supplies of goods and services received from non-"
"established taxable persons at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_12_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_12_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_12
msgid ""
"12. Supplies of goods and services received from taxable persons not "
"established in the republic of croatia at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_IN_ROC_13
#: model:account.tax,name:l10n_hr.1_VAT_S_IN_ROC_13
#: model:account.tax,name:l10n_hr.2_VAT_P_IN_ROC_13
#: model:account.tax,name:l10n_hr.2_VAT_S_IN_ROC_13
#: model:account.tax.template,name:l10n_hr.VAT_P_IN_ROC_13
#: model:account.tax.template,name:l10n_hr.VAT_S_IN_ROC_13
msgid "13%"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_G_IN_EU_13
#: model:account.tax,name:l10n_hr.2_VAT_P_G_IN_EU_13
#: model:account.tax.template,name:l10n_hr.VAT_P_G_IN_EU_13
msgid "13% EU G"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_S_IN_EU_13
#: model:account.tax,name:l10n_hr.2_VAT_P_S_IN_EU_13
#: model:account.tax.template,name:l10n_hr.VAT_P_S_IN_EU_13
msgid "13% EU S"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_NOT_IN_EU_13
#: model:account.tax,name:l10n_hr.2_VAT_P_NOT_IN_EU_13
#: model:account.tax.template,name:l10n_hr.VAT_P_NOT_IN_EU_13
msgid "13% EX"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_TAX_PERSON_13%
#: model:account.tax,name:l10n_hr.2_VAT_S_TAX_PERSON_13%
#: model:account.tax.template,name:l10n_hr.VAT_S_TAX_PERSON_13%
msgid "13% T P"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_13_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_13_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_13
msgid ""
"13. Input vat related to supplies of goods and services received from non-"
"established taxable persons at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_13_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_13_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_13
msgid ""
"13. Supplies of goods and services received from taxable persons not "
"established in the republic of croatia at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_14_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_14_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_14
msgid "14. Input vat related to importation"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_14_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_14_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_14
msgid ""
"14. Subsequent exemption on exportation related to personal  passenger "
"transportation"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_15_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_15_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_15
msgid "15. Adjustments of deductions"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_15_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_15_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_15
msgid "15. Vat calculated on importation"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_2_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_2_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_2
msgid "2. Input vat related to supplies received in the country at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_V_2_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_V_2
msgid ""
"2. Liability for the difference between tax and input tax in the taxation "
"period"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_2_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_2_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_2
msgid "2. Supplies carried out in another member state"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_2_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_2_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_2
msgid ""
"2. Supplies of goods and services in the republic of croatia at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_IV_2_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_IV_2
msgid "2. To refund"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_VI_2_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_VI_2
msgid "2. Total to refund"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_IN_ROC_25
#: model:account.tax,name:l10n_hr.1_VAT_S_IN_ROC_25
#: model:account.tax,name:l10n_hr.2_VAT_P_IN_ROC_25
#: model:account.tax,name:l10n_hr.2_VAT_S_IN_ROC_25
#: model:account.tax.template,name:l10n_hr.VAT_P_IN_ROC_25
#: model:account.tax.template,name:l10n_hr.VAT_S_IN_ROC_25
msgid "25%"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_G_IN_EU_25
#: model:account.tax,name:l10n_hr.2_VAT_P_G_IN_EU_25
#: model:account.tax.template,name:l10n_hr.VAT_P_G_IN_EU_25
msgid "25% EU G"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_S_IN_EU_25
#: model:account.tax,name:l10n_hr.2_VAT_P_S_IN_EU_25
#: model:account.tax.template,name:l10n_hr.VAT_P_S_IN_EU_25
msgid "25% EU S"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_NOT_IN_EU_25
#: model:account.tax,name:l10n_hr.2_VAT_P_NOT_IN_EU_25
#: model:account.tax.template,name:l10n_hr.VAT_P_NOT_IN_EU_25
msgid "25% EX"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_reverse_charge
#: model:account.tax,name:l10n_hr.2_VAT_P_reverse_charge
#: model:account.tax.template,name:l10n_hr.VAT_P_reverse_charge
msgid "25% R"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_TAX_PERSON_25%
#: model:account.tax,name:l10n_hr.2_VAT_S_TAX_PERSON_25%
#: model:account.tax.template,name:l10n_hr.VAT_S_TAX_PERSON_25%
msgid "25% T P"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_3_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_3_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_3
msgid "3. Input vat related to supplies received in the country at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_3_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_3_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_3
msgid ""
"3. Supplies of goods and services in the republic of croatia at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_3_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_3_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_3
msgid "3. Supplies of goods within the eu"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_4_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_4_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_4
msgid ""
"4. Input vat related to supplies received in the country in respect of which"
" the receiver calculates vat (domestic reverse charge)"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_4_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_4_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_4
msgid "4. Supplies of services within the eu"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_4_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_4_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_4
msgid ""
"4. Supplies received in the republic of croatia in respect of which the "
"receiver calculates vat (domestic reverse charge)"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_G_IN_EU_5
#: model:account.tax,name:l10n_hr.2_VAT_P_G_IN_EU_5
#: model:account.tax.template,name:l10n_hr.VAT_P_G_IN_EU_5
msgid "5% EU G"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_S_IN_EU_5
#: model:account.tax,name:l10n_hr.2_VAT_P_S_IN_EU_5
#: model:account.tax.template,name:l10n_hr.VAT_P_S_IN_EU_5
msgid "5% EU S"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_P_NOT_IN_EU_5
#: model:account.tax,name:l10n_hr.2_VAT_P_NOT_IN_EU_5
#: model:account.tax.template,name:l10n_hr.VAT_P_NOT_IN_EU_5
msgid "5% EX"
msgstr ""

#. module: l10n_hr
#: model:account.tax,name:l10n_hr.1_VAT_S_TAX_PERSON_5%
#: model:account.tax,name:l10n_hr.2_VAT_S_TAX_PERSON_5%
#: model:account.tax.template,name:l10n_hr.VAT_S_TAX_PERSON_5%
msgid "5% T P"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_5_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_5_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_5
msgid "5. Acquisition of goods within the eu at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_5_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_5_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_5
msgid ""
"5. Input vat related to acquisition of goods within the eu at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_5_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_5_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_5
msgid "5. Supplies to persons not established in the republic of croatia"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_6_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_6_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_6
msgid "6. Acquisition of goods within the eu at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_6_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_6_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_6
msgid ""
"6. Input vat related to acquisition of goods within the eu at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_6_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_6_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_6
msgid "6. Installed and assembled goods in another member state"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_7_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_7_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_7
msgid "7. Acquisition of goods within the eu at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_7_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_7_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_7
msgid ""
"7. Input vat related to acquisition of goods within the eu at a rate 25%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_7_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_7_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_7
msgid "7. Supplies of new means of transport to another member state"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_8_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_8_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_8
msgid "8. Domestic supplies"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_8_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_8_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_8
msgid "8. Input vat related to services received from eu at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_8_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_8_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_8
msgid "8. Services received from eu at a rate 5%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_9_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_9_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_I_9
msgid "9. Exportation"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_9_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_9_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_III_9
msgid "9. Input vat related to services received from eu at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_9_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_9_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_line_II_9
msgid "9. Services received from eu at a rate 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_221
#: model:account.account,name:l10n_hr.2_hr_221
#: model:account.account.template,name:l10n_hr.hr_221
msgid "Account payable from EU and third countries"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_22
#: model:account.group,name:l10n_hr.2_hr_group_22
#: model:account.group.template,name:l10n_hr.hr_group_22
msgid "Account payables, advances received and other payables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_290
#: model:account.account,name:l10n_hr.2_hr_290
#: model:account.account.template,name:l10n_hr.hr_290
msgid "Accrued expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_292
#: model:account.account,name:l10n_hr.2_hr_292
#: model:account.account.template,name:l10n_hr.hr_292
msgid "Accrued expense for goods purchases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_291
#: model:account.account,name:l10n_hr.2_hr_291
#: model:account.account.template,name:l10n_hr.hr_291
msgid "Accrued expense for rights to use"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_29
#: model:account.group,name:l10n_hr.2_hr_group_29
#: model:account.group.template,name:l10n_hr.hr_group_29
msgid "Accrued expenses and deferred income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_191
#: model:account.account,name:l10n_hr.2_hr_191
#: model:account.account.template,name:l10n_hr.hr_191
msgid "Accrued income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_193
#: model:account.account,name:l10n_hr.2_hr_193
#: model:account.account.template,name:l10n_hr.hr_193
msgid "Accrued interest which relates to future periods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_049
#: model:account.account,name:l10n_hr.2_hr_049
#: model:account.account.template,name:l10n_hr.hr_049
msgid "Accumulated depreciation of biological assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_029
#: model:account.account,name:l10n_hr.1_hr_059
#: model:account.account,name:l10n_hr.2_hr_029
#: model:account.account,name:l10n_hr.2_hr_059
#: model:account.account.template,name:l10n_hr.hr_029
#: model:account.account.template,name:l10n_hr.hr_059
msgid "Accumulated depreciation of buildings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_019
#: model:account.account,name:l10n_hr.2_hr_019
#: model:account.account.template,name:l10n_hr.hr_019
msgid "Accumulated depreciation of intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_039
#: model:account.account,name:l10n_hr.2_hr_039
#: model:account.account.template,name:l10n_hr.hr_039
msgid "Accumulated depreciation of plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_941
#: model:account.account,name:l10n_hr.2_hr_941
#: model:account.account.template,name:l10n_hr.hr_941
msgid "Accumulated loss"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_934
#: model:account.account,name:l10n_hr.2_hr_934
#: model:account.account.template,name:l10n_hr.hr_934
msgid "Actuarial gains/losses based on employment benefits"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_480
#: model:account.account,name:l10n_hr.2_hr_480
#: model:account.account.template,name:l10n_hr.hr_480
msgid "Additional rebates , discounts, claims and costs of samples"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_485
#: model:account.account,name:l10n_hr.2_hr_485
#: model:account.account.template,name:l10n_hr.hr_485
msgid "Additionally found operating expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_681
#: model:account.account,name:l10n_hr.2_hr_681
#: model:account.account.template,name:l10n_hr.hr_681
msgid "Additions on the property held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_056
#: model:account.account,name:l10n_hr.2_hr_056
#: model:account.account.template,name:l10n_hr.hr_056
msgid "Advance payments for investment properties"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_370
#: model:account.account,name:l10n_hr.2_hr_370
#: model:account.account.template,name:l10n_hr.hr_370
msgid "Advance payments for material"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_670
#: model:account.account,name:l10n_hr.2_hr_670
#: model:account.account.template,name:l10n_hr.hr_670
#: model:account.group,name:l10n_hr.1_hr_group_67
#: model:account.group,name:l10n_hr.2_hr_group_67
#: model:account.group.template,name:l10n_hr.hr_group_67
msgid "Advance payments for merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_671
#: model:account.account,name:l10n_hr.2_hr_671
#: model:account.account.template,name:l10n_hr.hr_671
msgid "Advance payments for merchandise goods import"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_046
#: model:account.account,name:l10n_hr.2_hr_046
#: model:account.account.template,name:l10n_hr.hr_046
msgid "Advance payments for purchase of biological assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_015
#: model:account.account,name:l10n_hr.2_hr_015
#: model:account.account.template,name:l10n_hr.hr_015
msgid "Advance payments for purchase of intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_026
#: model:account.account,name:l10n_hr.2_hr_026
#: model:account.account.template,name:l10n_hr.hr_026
msgid "Advance payments for purchase of land and buildings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_036
#: model:account.account,name:l10n_hr.2_hr_036
#: model:account.account.template,name:l10n_hr.hr_036
msgid "Advance payments for purchase of property, plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_372
#: model:account.account,name:l10n_hr.2_hr_372
#: model:account.account.template,name:l10n_hr.hr_372
msgid "Advance payments for small inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_371
#: model:account.account,name:l10n_hr.2_hr_371
#: model:account.account.template,name:l10n_hr.hr_371
msgid "Advance payments for spare parts"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_687
#: model:account.account,name:l10n_hr.2_hr_687
#: model:account.account.template,name:l10n_hr.hr_687
msgid "Advance payments for the purchase of property held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_374
#: model:account.account,name:l10n_hr.2_hr_374
#: model:account.account.template,name:l10n_hr.hr_374
msgid "Advance payments to foreign suppliers"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_373
#: model:account.account,name:l10n_hr.2_hr_373
#: model:account.account.template,name:l10n_hr.hr_373
msgid ""
"Advance payments to importer for raw materials, spare parts and small "
"inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_672
#: model:account.account,name:l10n_hr.2_hr_672
#: model:account.account.template,name:l10n_hr.hr_672
msgid "Advance payments to related party for merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140011
#: model:account.account,name:l10n_hr.2_hr_140011
#: model:account.account.template,name:l10n_hr.hr_140011
msgid "Advance tax - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140012
#: model:account.account,name:l10n_hr.2_hr_140012
#: model:account.account.template,name:l10n_hr.hr_140012
msgid "Advance tax - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140010
#: model:account.account,name:l10n_hr.2_hr_140010
#: model:account.account.template,name:l10n_hr.hr_140010
msgid "Advance tax - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140021
#: model:account.account,name:l10n_hr.2_hr_140021
#: model:account.account.template,name:l10n_hr.hr_140021
msgid "Advance tax from advances - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140022
#: model:account.account,name:l10n_hr.2_hr_140022
#: model:account.account.template,name:l10n_hr.hr_140022
msgid "Advance tax from advances - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140020
#: model:account.account,name:l10n_hr.2_hr_140020
#: model:account.account.template,name:l10n_hr.hr_140020
msgid "Advance tax from advances - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1404
#: model:account.account,name:l10n_hr.2_hr_1404
#: model:account.account.template,name:l10n_hr.hr_1404
msgid ""
"Advance tax from received deliveries of goods and services from taxpayers "
"without headquarters in the Republic of Croatia"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1401
#: model:account.account,name:l10n_hr.2_hr_1401
#: model:account.account.template,name:l10n_hr.hr_1401
msgid "Advance tax from transferred tax liability in the country - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14001
#: model:account.account,name:l10n_hr.2_hr_14001
#: model:account.account.template,name:l10n_hr.hr_14001
msgid "Advance tax on incoming invoices"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1400
#: model:account.account,name:l10n_hr.2_hr_1400
#: model:account.account.template,name:l10n_hr.hr_1400
msgid ""
"Advance tax on received deliveries and advances in the Republic of Croatia"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14021
#: model:account.account,name:l10n_hr.2_hr_14021
#: model:account.account.template,name:l10n_hr.hr_14021
msgid "Advance tax on the acquisition of goods from the EU - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14022
#: model:account.account,name:l10n_hr.2_hr_14022
#: model:account.account.template,name:l10n_hr.hr_14022
msgid "Advance tax on the acquisition of goods from the EU - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14020
#: model:account.account,name:l10n_hr.2_hr_14020
#: model:account.account.template,name:l10n_hr.hr_14020
msgid "Advance tax on the acquisition of goods from the EU - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_673
#: model:account.account,name:l10n_hr.2_hr_673
#: model:account.account.template,name:l10n_hr.hr_673
msgid "Advances for biological asset purchases"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_37
#: model:account.group,name:l10n_hr.2_hr_group_37
#: model:account.group.template,name:l10n_hr.hr_group_37
msgid ""
"Advance payments for raw materials, spare parts, small inventory and tires "
"purchases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_034
#: model:account.account,name:l10n_hr.2_hr_034
#: model:account.account.template,name:l10n_hr.hr_034
msgid "Agricultural equipment and machinery"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_49
#: model:account.group,name:l10n_hr.1_hr_group_59
#: model:account.group,name:l10n_hr.2_hr_group_49
#: model:account.group,name:l10n_hr.2_hr_group_59
#: model:account.group.template,name:l10n_hr.hr_group_49
#: model:account.group.template,name:l10n_hr.hr_group_59
msgid "Allocation of costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_490
#: model:account.account,name:l10n_hr.1_hr_590
#: model:account.account,name:l10n_hr.2_hr_490
#: model:account.account,name:l10n_hr.2_hr_590
#: model:account.account.template,name:l10n_hr.hr_490
#: model:account.account.template,name:l10n_hr.hr_590
msgid ""
"Allocation of costs to cost of conversion (cost of production) - (on acc. "
"60, 62, 63)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_591
#: model:account.account,name:l10n_hr.2_hr_591
#: model:account.account.template,name:l10n_hr.hr_591
msgid ""
"Allocation of costs to expenditures - to be shown in this year income "
"statement - (on acc. 72)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_491
#: model:account.account,name:l10n_hr.2_hr_491
#: model:account.account.template,name:l10n_hr.hr_491
msgid ""
"Allocation of management and administrative overheads to expense for the "
"year - (on acc. 72)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_430
#: model:account.account,name:l10n_hr.2_hr_430
#: model:account.account.template,name:l10n_hr.hr_430
msgid "Amortisation of intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_9312
#: model:account.account,name:l10n_hr.2_hr_9312
#: model:account.account.template,name:l10n_hr.hr_9312
msgid "An effective part of cash flow protection"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_9313
#: model:account.account,name:l10n_hr.2_hr_9313
#: model:account.account.template,name:l10n_hr.hr_9313
msgid "An effective part of the protection of net investment abroad"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_682
#: model:account.account,name:l10n_hr.2_hr_682
#: model:account.account.template,name:l10n_hr.hr_682
msgid "Arts held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_123
#: model:account.account,name:l10n_hr.2_hr_123
#: model:account.account.template,name:l10n_hr.hr_123
msgid "Associated undertakings interest receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_481
#: model:account.account,name:l10n_hr.2_hr_481
#: model:account.account.template,name:l10n_hr.hr_481
msgid "Bad debts and other assets write off"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_465
#: model:account.account,name:l10n_hr.2_hr_465
#: model:account.account.template,name:l10n_hr.hr_465
msgid "Bank charges and costs of payment operations"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_04
#: model:account.group,name:l10n_hr.1_hr_group_62
#: model:account.group,name:l10n_hr.2_hr_group_04
#: model:account.group,name:l10n_hr.2_hr_group_62
#: model:account.group.template,name:l10n_hr.hr_group_04
#: model:account.group.template,name:l10n_hr.hr_group_62
msgid "Biological assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_621
#: model:account.account,name:l10n_hr.2_hr_621
#: model:account.account.template,name:l10n_hr.hr_621
msgid "Biological assets for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_047
#: model:account.account,name:l10n_hr.2_hr_047
#: model:account.account.template,name:l10n_hr.hr_047
msgid "Biological assets in preparation (not put in use)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_620
#: model:account.account,name:l10n_hr.2_hr_620
#: model:account.account.template,name:l10n_hr.hr_620
msgid "Biological assets in progress"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_040
#: model:account.account,name:l10n_hr.2_hr_040
#: model:account.account.template,name:l10n_hr.hr_040
msgid "Biological assets- growing crops"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_041
#: model:account.account,name:l10n_hr.2_hr_041
#: model:account.account.template,name:l10n_hr.hr_041
msgid "Biological assets- live stock"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_023
#: model:account.account,name:l10n_hr.2_hr_023
#: model:account.account.template,name:l10n_hr.hr_023
msgid "Buildings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_994
#: model:account.account,name:l10n_hr.2_hr_994
#: model:account.account.template,name:l10n_hr.hr_994
msgid "Calculation of profit from investment in securities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_915
#: model:account.account,name:l10n_hr.2_hr_915
#: model:account.account.template,name:l10n_hr.hr_915
msgid "Capital gain from sale of treasury shares - stakes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_916
#: model:account.account,name:l10n_hr.2_hr_916
#: model:account.account.template,name:l10n_hr.hr_916
msgid "Capital gains on the sale of shares issued"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_918
#: model:account.account,name:l10n_hr.2_hr_918
#: model:account.account.template,name:l10n_hr.hr_918
msgid "Capital loss from sale of treasury shares - stakes"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_91
#: model:account.group,name:l10n_hr.2_hr_group_91
#: model:account.group.template,name:l10n_hr.hr_group_91
msgid "Capital reserves"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_911
#: model:account.account,name:l10n_hr.2_hr_911
#: model:account.account.template,name:l10n_hr.hr_911
msgid ""
"Capital reserves from additional payments in order to gain special rights in"
" the Company (convertible bonds)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_914
#: model:account.account,name:l10n_hr.2_hr_914
#: model:account.account.template,name:l10n_hr.hr_914
msgid "Capital reserves from other sources"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_912
#: model:account.account,name:l10n_hr.2_hr_912
#: model:account.account.template,name:l10n_hr.hr_912
msgid "Capital reserves from the additional payments of equity holders"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_106
#: model:account.account,name:l10n_hr.2_hr_106
#: model:account.account.template,name:l10n_hr.hr_106
msgid "Cash assigned to foreign currency purchases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_100
#: model:account.account,name:l10n_hr.2_hr_100
#: model:account.account.template,name:l10n_hr.hr_100
msgid "Cash in bank (transaction accounts)"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_10
#: model:account.group,name:l10n_hr.2_hr_group_10
#: model:account.group.template,name:l10n_hr.hr_group_10
msgid "Cash in bank and cash on hand"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_102
#: model:account.account,name:l10n_hr.2_hr_102
#: model:account.account.template,name:l10n_hr.hr_102
msgid "Cash on hand"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_1
#: model:account.group,name:l10n_hr.2_hr_group_1
#: model:account.group.template,name:l10n_hr.hr_group_1
msgid ""
"Cash, current financial assets,current receivables, prepaid expenses and "
"accrued income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1407
#: model:account.account,name:l10n_hr.2_hr_1407
#: model:account.account.template,name:l10n_hr.hr_1407
msgid ""
"Claims for the difference of higher input tax than liabilities in the "
"taxation period"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_011
#: model:account.account,name:l10n_hr.2_hr_011
#: model:account.account.template,name:l10n_hr.hr_011
msgid "Concession rights, patents, commodity and service brands"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_706
#: model:account.account,name:l10n_hr.2_hr_706
#: model:account.account.template,name:l10n_hr.hr_706
msgid "Construction contracts losses"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_422
#: model:account.account,name:l10n_hr.2_hr_422
#: model:account.account.template,name:l10n_hr.hr_422
msgid "Contributions from salaries costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_423
#: model:account.account,name:l10n_hr.2_hr_423
#: model:account.account.template,name:l10n_hr.hr_423
msgid "Contributions on salaries costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140031
#: model:account.account,name:l10n_hr.2_hr_140031
#: model:account.account.template,name:l10n_hr.hr_140031
msgid "Correction of input tax due to conversion of goods (exempt deliveries)"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_5
#: model:account.group,name:l10n_hr.2_hr_group_5
#: model:account.group.template,name:l10n_hr.hr_group_5
msgid "Cost centres and cost driver"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_477
#: model:account.account,name:l10n_hr.2_hr_477
#: model:account.account.template,name:l10n_hr.hr_477
msgid "Cost of discounts given"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_70
#: model:account.group,name:l10n_hr.2_hr_group_70
#: model:account.group.template,name:l10n_hr.hr_group_70
msgid "Cost of finished goods sold and cost of services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_715
#: model:account.account,name:l10n_hr.2_hr_715
#: model:account.account.template,name:l10n_hr.hr_715
msgid ""
"Cost of good sold which relates to previous periods- by mistake not show in "
"previous periods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_700
#: model:account.account,name:l10n_hr.2_hr_700
#: model:account.account.template,name:l10n_hr.hr_700
#: model:account.group,name:l10n_hr.1_hr_group_71
#: model:account.group,name:l10n_hr.2_hr_group_71
#: model:account.group.template,name:l10n_hr.hr_group_71
msgid "Cost of goods sold"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_463
#: model:account.account,name:l10n_hr.2_hr_463
#: model:account.account.template,name:l10n_hr.hr_463
msgid "Cost of internal representation and promotion"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_309
#: model:account.account,name:l10n_hr.2_hr_309
#: model:account.account.template,name:l10n_hr.hr_309
#: model:account.group,name:l10n_hr.1_hr_group_30
#: model:account.group,name:l10n_hr.2_hr_group_30
#: model:account.group.template,name:l10n_hr.hr_group_30
msgid "Cost of inventory purchasing calculation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_720
#: model:account.account,name:l10n_hr.2_hr_720
#: model:account.account.template,name:l10n_hr.hr_720
msgid "Cost of management, selling costs and administrative overheads"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_401
#: model:account.account,name:l10n_hr.2_hr_401
#: model:account.account.template,name:l10n_hr.hr_401
msgid "Cost of material used in administration and sale department"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_711
#: model:account.account,name:l10n_hr.2_hr_711
#: model:account.account.template,name:l10n_hr.hr_711
msgid "Cost of property which is classified as held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_400
#: model:account.account,name:l10n_hr.2_hr_400
#: model:account.account.template,name:l10n_hr.hr_400
msgid "Cost of raw material"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_703
#: model:account.account,name:l10n_hr.2_hr_703
#: model:account.account.template,name:l10n_hr.hr_703
msgid "Cost of sale of material and obsolete inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_404
#: model:account.account,name:l10n_hr.2_hr_404
#: model:account.account.template,name:l10n_hr.hr_404
msgid "Cost of small inventory, packaging and tires"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_468
#: model:account.account,name:l10n_hr.2_hr_468
#: model:account.account.template,name:l10n_hr.hr_468
msgid "Cost of usage rights (excluding leases) and expense of board members"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_708
#: model:account.account,name:l10n_hr.2_hr_708
#: model:account.account.template,name:l10n_hr.hr_708
msgid ""
"Cost of value adjustments of work in progress, semi-finished goods and "
"finished goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_461
#: model:account.account,name:l10n_hr.2_hr_461
#: model:account.account.template,name:l10n_hr.hr_461
msgid "Cost reimbursements, allowances and scholarships"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_4
#: model:account.group,name:l10n_hr.2_hr_group_4
#: model:account.group.template,name:l10n_hr.hr_group_4
msgid "Costs by nature , financial and other expenses"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_408
#: model:account.account,name:l10n_hr.2_hr_408
#: model:account.account.template,name:l10n_hr.hr_408
msgid "Costs for samples"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_707
#: model:account.account,name:l10n_hr.2_hr_707
#: model:account.account.template,name:l10n_hr.hr_707
msgid "Costs in relation with partnership agreement"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_713
#: model:account.account,name:l10n_hr.2_hr_713
#: model:account.account.template,name:l10n_hr.hr_713
msgid ""
"Costs of goods shortages ( due to evaporate), damages , breakage and goods "
"write off"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_710
#: model:account.account,name:l10n_hr.2_hr_710
#: model:account.account.template,name:l10n_hr.hr_710
msgid "Costs of goods sold"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_701
#: model:account.account,name:l10n_hr.2_hr_701
#: model:account.account.template,name:l10n_hr.hr_701
msgid "Costs of rendering services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_714
#: model:account.account,name:l10n_hr.2_hr_714
#: model:account.account.template,name:l10n_hr.hr_714
msgid "Costs of replacement of goods within the warranty period"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_702
#: model:account.account,name:l10n_hr.2_hr_702
#: model:account.account.template,name:l10n_hr.hr_702
msgid "Costs of unused capacity"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_718
#: model:account.account,name:l10n_hr.2_hr_718
#: model:account.account.template,name:l10n_hr.hr_718
msgid "Costs of value adjustments of merchandise goods and advance payments"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_712
#: model:account.account,name:l10n_hr.2_hr_712
#: model:account.account.template,name:l10n_hr.hr_712
msgid "Costs which relates to property, plant and equipment held for sale"
msgstr ""

#. module: l10n_hr
#: model:ir.ui.menu,name:l10n_hr.account_reports_hr_statements_menu
msgid "Croatia"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_016
#: model:account.account,name:l10n_hr.2_hr_016
#: model:account.account.template,name:l10n_hr.hr_016
msgid "Cryptocurrencies"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_2
#: model:account.group,name:l10n_hr.2_hr_group_2
#: model:account.group.template,name:l10n_hr.hr_group_2
msgid ""
"Current and non-current liabilities, long term provisions, accrued expenses "
"and deferred income"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_11
#: model:account.group,name:l10n_hr.2_hr_group_11
#: model:account.group.template,name:l10n_hr.hr_group_11
msgid "Current financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_21
#: model:account.group,name:l10n_hr.2_hr_group_21
#: model:account.group.template,name:l10n_hr.hr_group_21
msgid "Current financial liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_24
#: model:account.group,name:l10n_hr.2_hr_group_24
#: model:account.group.template,name:l10n_hr.hr_group_24
msgid "Current liabilities for taxes, contributions and similar expenses"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_20
#: model:account.group,name:l10n_hr.2_hr_group_20
#: model:account.group.template,name:l10n_hr.hr_group_20
msgid ""
"Current liabilities towards group companies and associated undertakings"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_12
#: model:account.group,name:l10n_hr.2_hr_group_12
#: model:account.group.template,name:l10n_hr.hr_group_12
msgid "Current receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_460
#: model:account.account,name:l10n_hr.2_hr_460
#: model:account.account.template,name:l10n_hr.hr_460
msgid "Daily allowances for business trips and other travel expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_913
#: model:account.account,name:l10n_hr.2_hr_913
#: model:account.account.template,name:l10n_hr.hr_913
msgid "Decrease of share capital transferred to capital reserve"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_295
#: model:account.account,name:l10n_hr.2_hr_295
#: model:account.account.template,name:l10n_hr.hr_295
msgid "Deferred income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_293
#: model:account.account,name:l10n_hr.2_hr_293
#: model:account.account.template,name:l10n_hr.hr_293
msgid "Deferred income (income which relates to future period)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_294
#: model:account.account,name:l10n_hr.2_hr_294
#: model:account.account.template,name:l10n_hr.hr_294
msgid "Deferred income from government grants"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_296
#: model:account.account,name:l10n_hr.2_hr_296
#: model:account.account.template,name:l10n_hr.hr_296
msgid ""
"Deferred income relating to not invoiced but shipped goods and services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_298
#: model:account.account,name:l10n_hr.2_hr_298
#: model:account.account.template,name:l10n_hr.hr_298
msgid "Deferred invoiced income without delivery"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_08
#: model:account.group,name:l10n_hr.2_hr_group_08
#: model:account.group.template,name:l10n_hr.hr_group_08
msgid "Deferred tax asset"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_260
#: model:account.account,name:l10n_hr.2_hr_260
#: model:account.account.template,name:l10n_hr.hr_260
msgid "Deferred tax liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_26
#: model:account.group,name:l10n_hr.2_hr_group_26
#: model:account.group.template,name:l10n_hr.hr_group_26
msgid "Deferred taxes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_080
#: model:account.account,name:l10n_hr.2_hr_080
#: model:account.account.template,name:l10n_hr.hr_080
msgid "Deferred temporary tax difference on income tax"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_43
#: model:account.group,name:l10n_hr.2_hr_group_43
#: model:account.group.template,name:l10n_hr.hr_group_43
msgid "Depreciation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_512
#: model:account.account,name:l10n_hr.2_hr_512
#: model:account.account.template,name:l10n_hr.hr_512
msgid "Depreciation in selling costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_435
#: model:account.account,name:l10n_hr.2_hr_435
#: model:account.account.template,name:l10n_hr.hr_435
msgid "Depreciation of biological assets (vineyards, orchards, live stock)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_432
#: model:account.account,name:l10n_hr.2_hr_432
#: model:account.account.template,name:l10n_hr.hr_432
msgid "Depreciation of cars and other vehicles for personal transportation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_433
#: model:account.account,name:l10n_hr.2_hr_433
#: model:account.account.template,name:l10n_hr.hr_433
msgid ""
"Depreciation of property, plant and - management and selling department"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_431
#: model:account.account,name:l10n_hr.2_hr_431
#: model:account.account.template,name:l10n_hr.hr_431
msgid "Depreciation of property, plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_434
#: model:account.account,name:l10n_hr.2_hr_434
#: model:account.account.template,name:l10n_hr.hr_434
msgid "Depreciation surplus due to revaluation of assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_502
#: model:account.account,name:l10n_hr.2_hr_502
#: model:account.account.template,name:l10n_hr.hr_502
msgid "Depreciation used in management and administrative overheads"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_197
#: model:account.account,name:l10n_hr.2_hr_197
#: model:account.account.template,name:l10n_hr.hr_197
msgid "Derivatives- hedging instruments"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_79
#: model:account.group,name:l10n_hr.2_hr_group_79
#: model:account.group.template,name:l10n_hr.hr_group_79
msgid "Difference between income and expenses"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_482
#: model:account.account,name:l10n_hr.2_hr_482
#: model:account.account.template,name:l10n_hr.hr_482
msgid "Disposals of property, plant and equipment and intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_775
#: model:account.account,name:l10n_hr.2_hr_775
#: model:account.account.template,name:l10n_hr.hr_775
msgid "Dividends income from associated undertakings"
msgstr ""

#. module: l10n_hr
#: model:account.fiscal.position,name:l10n_hr.1_fiscal_position_hr_national
#: model:account.fiscal.position,name:l10n_hr.2_fiscal_position_hr_national
#: model:account.fiscal.position.template,name:l10n_hr.fiscal_position_hr_national
msgid "Domestic"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_reverse_charge
#: model:account.tax,description:l10n_hr.2_VAT_P_reverse_charge
#: model:account.tax.template,description:l10n_hr.VAT_P_reverse_charge
msgid "Domestic reverse charge"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_220
#: model:account.account,name:l10n_hr.2_hr_220
#: model:account.account.template,name:l10n_hr.hr_220
msgid "Domestic trade payable"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_302
#: model:account.account,name:l10n_hr.2_hr_302
#: model:account.account.template,name:l10n_hr.hr_302
msgid "Duties and other customs duty"
msgstr ""

#. module: l10n_hr
#: model:account.fiscal.position,name:l10n_hr.1_fiscal_position_hr_eu
#: model:account.fiscal.position,name:l10n_hr.2_fiscal_position_hr_eu
#: model:account.fiscal.position.template,name:l10n_hr.fiscal_position_hr_eu
msgid "EU partner"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_407
#: model:account.account,name:l10n_hr.2_hr_407
#: model:account.account.template,name:l10n_hr.hr_407
msgid "Energy - administration and sale department"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_406
#: model:account.account,name:l10n_hr.2_hr_406
#: model:account.account.template,name:l10n_hr.hr_406
msgid "Energy used during production of goods and services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_031
#: model:account.account,name:l10n_hr.2_hr_031
#: model:account.account.template,name:l10n_hr.hr_031
msgid "Equipment"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_9
#: model:account.group,name:l10n_hr.2_hr_group_9
#: model:account.group.template,name:l10n_hr.hr_group_9
msgid "Equity and reserves and of the balance sheet items"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_475
#: model:account.account,name:l10n_hr.2_hr_475
#: model:account.account.template,name:l10n_hr.hr_475
msgid ""
"Exchange difference on translation of foreign operations with third party"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7703
#: model:account.account,name:l10n_hr.2_hr_7703
#: model:account.account.template,name:l10n_hr.hr_7703
msgid ""
"Exchange rate differences and other financial income from relations with "
"entrepreneurs within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_303
#: model:account.account,name:l10n_hr.2_hr_303
#: model:account.account.template,name:l10n_hr.hr_303
msgid "Excise duties which can not be deducted"
msgstr ""

#. module: l10n_hr
#: model:account.fiscal.position,name:l10n_hr.1_fiscal_position_hr_exempt
#: model:account.fiscal.position,name:l10n_hr.2_fiscal_position_hr_exempt
#: model:account.fiscal.position.template,name:l10n_hr.fiscal_position_hr_exempt
msgid "Exempt taxpayer"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_731
#: model:account.account,name:l10n_hr.2_hr_731
#: model:account.account.template,name:l10n_hr.hr_731
msgid "Expense based on material and unexpected asset disposals"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_733
#: model:account.account,name:l10n_hr.2_hr_733
#: model:account.account.template,name:l10n_hr.hr_733
msgid "Expense based on material and unexpected events"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_488
#: model:account.account,name:l10n_hr.2_hr_488
#: model:account.account.template,name:l10n_hr.hr_488
msgid "Expense from other activities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_705
#: model:account.account,name:l10n_hr.2_hr_705
#: model:account.account.template,name:l10n_hr.hr_705
msgid ""
"Expense which relates to previous periods- by mistake not show in previous "
"periods"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_7
#: model:account.group,name:l10n_hr.2_hr_group_7
#: model:account.group.template,name:l10n_hr.hr_group_7
msgid "Expenses and income for the year"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_411
#: model:account.account,name:l10n_hr.2_hr_411
#: model:account.account.template,name:l10n_hr.hr_411
msgid "External service costs used in goods production and providing services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_9310
#: model:account.account,name:l10n_hr.2_hr_9310
#: model:account.account.template,name:l10n_hr.hr_9310
msgid "Fair value of available-for-sale financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_466
#: model:account.account,name:l10n_hr.2_hr_466
#: model:account.account.template,name:l10n_hr.hr_466
msgid "Fees, compensations and other expense"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_47
#: model:account.group,name:l10n_hr.2_hr_group_47
#: model:account.group.template,name:l10n_hr.hr_group_47
msgid "Financial expenses"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_778
#: model:account.account,name:l10n_hr.2_hr_778
#: model:account.account.template,name:l10n_hr.hr_778
msgid "Financial fees income"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_77
#: model:account.group,name:l10n_hr.2_hr_group_77
#: model:account.group.template,name:l10n_hr.hr_group_77
msgid "Financial income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_774
#: model:account.account,name:l10n_hr.2_hr_774
#: model:account.account.template,name:l10n_hr.hr_774
msgid "Financial income from long term financial asset and loans given"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_8
#: model:account.group,name:l10n_hr.2_hr_group_8
#: model:account.group.template,name:l10n_hr.hr_group_8
msgid "Financial result"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_63
#: model:account.group,name:l10n_hr.2_hr_group_63
#: model:account.group.template,name:l10n_hr.hr_group_63
msgid "Finished goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_638
#: model:account.account,name:l10n_hr.2_hr_638
#: model:account.account.template,name:l10n_hr.hr_638
msgid "Finished goods from partnership agreement"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_632
#: model:account.account,name:l10n_hr.2_hr_632
#: model:account.account.template,name:l10n_hr.hr_632
msgid "Finished goods given in commission sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_633
#: model:account.account,name:l10n_hr.2_hr_633
#: model:account.account.template,name:l10n_hr.hr_633
msgid "Finished goods given in consignment sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_635
#: model:account.account,name:l10n_hr.2_hr_635
#: model:account.account.template,name:l10n_hr.hr_635
msgid "Finished goods in free-trade zone"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_640
#: model:account.account,name:l10n_hr.2_hr_640
#: model:account.account.template,name:l10n_hr.hr_640
#: model:account.group,name:l10n_hr.1_hr_group_64
#: model:account.group,name:l10n_hr.2_hr_group_64
#: model:account.group.template,name:l10n_hr.hr_group_64
msgid "Finished goods in own stores"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_634
#: model:account.account,name:l10n_hr.1_hr_666
#: model:account.account,name:l10n_hr.2_hr_634
#: model:account.account,name:l10n_hr.2_hr_666
#: model:account.account.template,name:l10n_hr.hr_634
#: model:account.account.template,name:l10n_hr.hr_666
msgid "Finished goods in processing, finishing and manipulation phases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_631
#: model:account.account,name:l10n_hr.2_hr_631
#: model:account.account.template,name:l10n_hr.hr_631
msgid "Finished goods in public warehouse"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_637
#: model:account.account,name:l10n_hr.2_hr_637
#: model:account.account.template,name:l10n_hr.hr_637
msgid "Finished goods in storefront"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_630
#: model:account.account,name:l10n_hr.2_hr_630
#: model:account.account.template,name:l10n_hr.hr_630
msgid "Finished goods in warehouse"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_024
#: model:account.account,name:l10n_hr.2_hr_024
#: model:account.account.template,name:l10n_hr.hr_024
msgid "Flats for employees"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_105
#: model:account.account,name:l10n_hr.2_hr_105
#: model:account.account.template,name:l10n_hr.hr_105
msgid "Foreign cash on hand"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_103
#: model:account.account,name:l10n_hr.2_hr_103
#: model:account.account.template,name:l10n_hr.hr_103
msgid "Foreign currency accounts"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_932
#: model:account.account,name:l10n_hr.2_hr_932
#: model:account.account.template,name:l10n_hr.hr_932
msgid "Foreign currency from foreign translation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_471
#: model:account.account,name:l10n_hr.2_hr_471
#: model:account.account.template,name:l10n_hr.hr_471
msgid "Foreign exchange difference and other expense in a Group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_126
#: model:account.account,name:l10n_hr.2_hr_126
#: model:account.account.template,name:l10n_hr.hr_126
msgid "Foreign trade asset receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_121
#: model:account.account,name:l10n_hr.2_hr_121
#: model:account.account.template,name:l10n_hr.hr_121
msgid "Foreign trade customers-EU and foreign"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_787
#: model:account.account,name:l10n_hr.2_hr_787
#: model:account.account.template,name:l10n_hr.hr_787
msgid "Gains from biological assets arising from valuation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_487
#: model:account.account,name:l10n_hr.2_hr_487
#: model:account.account.template,name:l10n_hr.hr_487
msgid "Gifts/donations above 2% of total income and other donations"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_486
#: model:account.account,name:l10n_hr.2_hr_486
#: model:account.account.template,name:l10n_hr.hr_486
msgid "Gifts/donations up to 2% of total income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_013
#: model:account.account,name:l10n_hr.2_hr_013
#: model:account.account.template,name:l10n_hr.hr_013
msgid "Goodwill"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_905
#: model:account.account,name:l10n_hr.2_hr_905
#: model:account.account.template,name:l10n_hr.hr_905
msgid "Government capital in company"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_424
#: model:account.account,name:l10n_hr.2_hr_424
#: model:account.account.template,name:l10n_hr.hr_424
msgid "Gross salaries"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_122
#: model:account.account,name:l10n_hr.2_hr_122
#: model:account.account.template,name:l10n_hr.hr_122
msgid "Group companies receivables"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_I_0_tag_column1
#: model:account.report.line,name:l10n_hr.tax_report_title_transactions0
msgid ""
"I. Transactions not subject to vat and exempt - total value "
"(1.+2.+3.+4.+5.+6.+7.+8.+9.+10.)"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_0_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_II_0_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_title_transactions1
msgid ""
"II. Taxable transactions – total amount "
"(1.+2.+3.+4.+5.+6.+7.+8.+9.+10.+11.+12.+13.+14.+15.)"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_0_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_III_0_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_title_calculed_vat
msgid ""
"III. Calculated input vat – total amount "
"(1.+2.+3.+4.+5.+6.+7.+8.+9.+10.+11.+12.+13. +14.+15.)"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_IV_0_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_title_vat_liab
msgid ""
"IV. Vat liability in the calculation period: to pay (ii. - iii.) or to "
"refund (iii. - ii.)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_719
#: model:account.account,name:l10n_hr.2_hr_719
#: model:account.account.template,name:l10n_hr.hr_719
msgid ""
"Impairment losses recognized on property, plant and equipment held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_699
#: model:account.account,name:l10n_hr.2_hr_699
#: model:account.account.template,name:l10n_hr.hr_699
msgid "Impairment of property, plant and equipment held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_773
#: model:account.account,name:l10n_hr.2_hr_773
#: model:account.account.template,name:l10n_hr.hr_773
msgid "Income from dividends and share in result"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_772
#: model:account.account,name:l10n_hr.2_hr_772
#: model:account.account.template,name:l10n_hr.hr_772
msgid ""
"Income from exchange difference on translation of foreign operations and "
"other income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_786
#: model:account.account,name:l10n_hr.2_hr_786
#: model:account.account.template,name:l10n_hr.hr_786
msgid "Income from government grants"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7839
#: model:account.account,name:l10n_hr.2_hr_7839
#: model:account.account.template,name:l10n_hr.hr_7839
msgid ""
"Income from grants and compensation from entrepreneurs within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_783
#: model:account.account,name:l10n_hr.2_hr_783
#: model:account.account.template,name:l10n_hr.hr_783
msgid "Income from grants, subsidies and compensations"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7700
#: model:account.account,name:l10n_hr.2_hr_7700
#: model:account.account.template,name:l10n_hr.hr_7700
msgid ""
"Income from investments in shares (shares) of entrepreneurs within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_780
#: model:account.account,name:l10n_hr.2_hr_780
#: model:account.account.template,name:l10n_hr.hr_780
msgid "Income from liability write off and from discounts received"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_784
#: model:account.account,name:l10n_hr.2_hr_784
#: model:account.account.template,name:l10n_hr.hr_784
msgid "Income from losses and revaluations reversals"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_777
#: model:account.account,name:l10n_hr.2_hr_777
#: model:account.account.template,name:l10n_hr.hr_777
msgid "Income from negative goodwill"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7701
#: model:account.account,name:l10n_hr.2_hr_7701
#: model:account.account.template,name:l10n_hr.hr_7701
msgid ""
"Income from other long-term financial investments and loans to entrepreneurs"
" within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_782
#: model:account.account,name:l10n_hr.2_hr_782
#: model:account.account.template,name:l10n_hr.hr_782
msgid ""
"Income from reversal of value adjustment and provisions and collected "
"receivables written off"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_764
#: model:account.account,name:l10n_hr.2_hr_764
#: model:account.account.template,name:l10n_hr.hr_764
msgid "Income from sale of goods on commodity loan granted"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_766
#: model:account.account,name:l10n_hr.2_hr_766
#: model:account.account.template,name:l10n_hr.hr_766
msgid "Income from sale of goods on financial leasing"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_767
#: model:account.account,name:l10n_hr.2_hr_767
#: model:account.account.template,name:l10n_hr.hr_767
msgid ""
"Income from sale of property, plant and equipment and arts (held for sale)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_763
#: model:account.account,name:l10n_hr.2_hr_763
#: model:account.account.template,name:l10n_hr.hr_763
msgid "Income from sale of slow-moving inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7809
#: model:account.account,name:l10n_hr.2_hr_7809
#: model:account.account.template,name:l10n_hr.hr_7809
msgid ""
"Income from the write-off of liabilities to entrepreneurs within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_421
#: model:account.account,name:l10n_hr.2_hr_421
#: model:account.account.template,name:l10n_hr.hr_421
msgid "Income tax and surtax costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_803
#: model:account.account,name:l10n_hr.2_hr_803
#: model:account.account.template,name:l10n_hr.hr_803
msgid "Income tax expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_243
#: model:account.account,name:l10n_hr.2_hr_243
#: model:account.account.template,name:l10n_hr.hr_243
msgid "Income tax payable, tax on investment income and withholding tax"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140032
#: model:account.account,name:l10n_hr.2_hr_140032
#: model:account.account.template,name:l10n_hr.hr_140032
msgid ""
"Input tax correction due to a change in input tax recognition percentage - "
"adjustment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14003
#: model:account.account,name:l10n_hr.2_hr_14003
#: model:account.account.template,name:l10n_hr.hr_14003
msgid "Input tax corrections"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1403
#: model:account.account,name:l10n_hr.2_hr_1403
#: model:account.account.template,name:l10n_hr.hr_1403
msgid "Input tax from services received from the EU"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1402
#: model:account.account,name:l10n_hr.2_hr_1402
#: model:account.account.template,name:l10n_hr.hr_1402
msgid "Input tax from the acquisition of goods within the EU"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1408
#: model:account.account,name:l10n_hr.2_hr_1408
#: model:account.account.template,name:l10n_hr.hr_1408
msgid "Input tax not yet recognized"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_464
#: model:account.account,name:l10n_hr.2_hr_464
#: model:account.account.template,name:l10n_hr.hr_464
msgid "Insurance premiums"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_01
#: model:account.group,name:l10n_hr.2_hr_group_01
#: model:account.group.template,name:l10n_hr.hr_group_01
msgid "Intangible asset"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_690
#: model:account.account,name:l10n_hr.2_hr_690
#: model:account.account.template,name:l10n_hr.hr_690
msgid "Intangible assets held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_017
#: model:account.account,name:l10n_hr.2_hr_017
#: model:account.account.template,name:l10n_hr.hr_017
msgid "Intangible assets in preparation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_416
#: model:account.account,name:l10n_hr.2_hr_416
#: model:account.account.template,name:l10n_hr.hr_416
msgid "Intellectual services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_470
#: model:account.account,name:l10n_hr.2_hr_470
#: model:account.account.template,name:l10n_hr.hr_470
msgid "Interest expense from Group companies"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_473
#: model:account.account,name:l10n_hr.2_hr_473
#: model:account.account.template,name:l10n_hr.hr_473
msgid "Interest expense from operations with third party"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_771
#: model:account.account,name:l10n_hr.2_hr_771
#: model:account.account.template,name:l10n_hr.hr_771
msgid "Interest income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_124
#: model:account.account,name:l10n_hr.2_hr_124
#: model:account.account.template,name:l10n_hr.hr_124
msgid "Interest receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_232
#: model:account.account,name:l10n_hr.2_hr_232
#: model:account.account.template,name:l10n_hr.hr_232
msgid "Interests payable"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_318
#: model:account.account,name:l10n_hr.2_hr_318
#: model:account.account.template,name:l10n_hr.hr_318
msgid "Inventory price adjustment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_483
#: model:account.account,name:l10n_hr.2_hr_483
#: model:account.account.template,name:l10n_hr.hr_483
msgid "Inventory shortages and burglary costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_449
#: model:account.account,name:l10n_hr.2_hr_449
#: model:account.account.template,name:l10n_hr.hr_449
msgid "Inventory write off"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_05
#: model:account.group,name:l10n_hr.2_hr_group_05
#: model:account.group.template,name:l10n_hr.hr_group_05
msgid "Investment properties"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_051
#: model:account.account,name:l10n_hr.2_hr_051
#: model:account.account.template,name:l10n_hr.hr_051
msgid "Investment property- buildings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_050
#: model:account.account,name:l10n_hr.2_hr_050
#: model:account.account.template,name:l10n_hr.hr_050
msgid "Investment property-land"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_064
#: model:account.account,name:l10n_hr.2_hr_064
#: model:account.account.template,name:l10n_hr.hr_064
msgid "Investments in a securities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_066
#: model:account.account,name:l10n_hr.2_hr_066
#: model:account.account.template,name:l10n_hr.hr_066
msgid "Investments in associates accounted for using the equity method"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1121
#: model:account.account,name:l10n_hr.2_hr_1121
#: model:account.account.template,name:l10n_hr.hr_1121
msgid ""
"Investments in other securities  of companies connected by participating "
"interests"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_0621
#: model:account.account,name:l10n_hr.2_hr_0621
#: model:account.account.template,name:l10n_hr.hr_0621
msgid ""
"Investments in other securities of companies connected by participating "
"interests"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_0601
#: model:account.account,name:l10n_hr.1_hr_1101
#: model:account.account,name:l10n_hr.2_hr_0601
#: model:account.account,name:l10n_hr.2_hr_1101
#: model:account.account.template,name:l10n_hr.hr_0601
#: model:account.account.template,name:l10n_hr.hr_1101
msgid "Investments in other securities of entrepreneurs within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_114
#: model:account.account,name:l10n_hr.2_hr_114
#: model:account.account.template,name:l10n_hr.hr_114
msgid "Investments in securities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_0620
#: model:account.account,name:l10n_hr.1_hr_1120
#: model:account.account,name:l10n_hr.2_hr_0620
#: model:account.account,name:l10n_hr.2_hr_1120
#: model:account.account.template,name:l10n_hr.hr_0620
#: model:account.account.template,name:l10n_hr.hr_1120
msgid ""
"Investments in shares (shares) of companies connected by participating "
"interests"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_0600
#: model:account.account,name:l10n_hr.1_hr_1100
#: model:account.account,name:l10n_hr.2_hr_0600
#: model:account.account,name:l10n_hr.2_hr_1100
#: model:account.account.template,name:l10n_hr.hr_0600
#: model:account.account.template,name:l10n_hr.hr_1100
msgid "Investments in shares (shares) of entrepreneurs within the group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_917
#: model:account.account,name:l10n_hr.2_hr_917
#: model:account.account.template,name:l10n_hr.hr_917
msgid "Investments in silent company transferred to capital reserves"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_919
#: model:account.account,name:l10n_hr.2_hr_919
#: model:account.account.template,name:l10n_hr.hr_919
msgid "Investments of entrepreneur transferred to capital reserves"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_057
#: model:account.account,name:l10n_hr.2_hr_057
#: model:account.account.template,name:l10n_hr.hr_057
msgid "Investments property under construction"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_901
#: model:account.account,name:l10n_hr.2_hr_901
#: model:account.account.template,name:l10n_hr.hr_901
msgid "Issued capital - non controlling interests"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_900
#: model:account.account,name:l10n_hr.2_hr_900
#: model:account.account.template,name:l10n_hr.hr_900
msgid "Issued capital - paid in"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_902
#: model:account.account,name:l10n_hr.2_hr_902
#: model:account.account.template,name:l10n_hr.hr_902
msgid "Issued capital- not paid in"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_020
#: model:account.account,name:l10n_hr.2_hr_020
#: model:account.account.template,name:l10n_hr.hr_020
msgid "Land"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_02
#: model:account.group,name:l10n_hr.2_hr_group_02
#: model:account.group.template,name:l10n_hr.hr_group_02
msgid "Land and buildings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_027
#: model:account.account,name:l10n_hr.2_hr_027
#: model:account.account.template,name:l10n_hr.hr_027
msgid "Land and buildings under construction"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_214
#: model:account.account,name:l10n_hr.2_hr_214
#: model:account.account.template,name:l10n_hr.hr_214
msgid "Lease liabilities and deposits received"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_115
#: model:account.account,name:l10n_hr.2_hr_115
#: model:account.account.template,name:l10n_hr.hr_115
msgid "Leases given, deposits and other"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_414
#: model:account.account,name:l10n_hr.2_hr_414
#: model:account.account.template,name:l10n_hr.hr_414
msgid "Leasing costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_920
#: model:account.account,name:l10n_hr.2_hr_920
#: model:account.account.template,name:l10n_hr.hr_920
msgid "Legal reserves"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_101
#: model:account.account,name:l10n_hr.2_hr_101
#: model:account.account.template,name:l10n_hr.hr_101
msgid "Letter of credit issued in domestic bank"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_104
#: model:account.account,name:l10n_hr.2_hr_104
#: model:account.account.template,name:l10n_hr.hr_104
msgid "Letter of credit issued in foreign bank"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_238
#: model:account.account,name:l10n_hr.2_hr_238
#: model:account.account.template,name:l10n_hr.hr_238
msgid "Liabilities due to acquisitions of shares/stakes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_218
#: model:account.account,name:l10n_hr.2_hr_218
#: model:account.account.template,name:l10n_hr.hr_218
msgid ""
"Liabilities due to asset_non_current held for sale (this account relates to "
"due loan liabilities and account payable for asset which is held for sale "
"(acc.69))"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_999
#: model:account.account,name:l10n_hr.2_hr_999
#: model:account.account.template,name:l10n_hr.hr_999
msgid "Liabilities due to investment project"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_210
#: model:account.account,name:l10n_hr.2_hr_210
#: model:account.account.template,name:l10n_hr.hr_210
msgid "Liabilities due to issued cheques"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_211
#: model:account.account,name:l10n_hr.2_hr_211
#: model:account.account.template,name:l10n_hr.hr_211
msgid "Liabilities due to issued promissory notes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_212
#: model:account.account,name:l10n_hr.2_hr_212
#: model:account.account.template,name:l10n_hr.hr_212
msgid "Liabilities due to issued securities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_236
#: model:account.account,name:l10n_hr.2_hr_236
#: model:account.account.template,name:l10n_hr.hr_236
msgid "Liabilities due to operations in foreign trade"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_235
#: model:account.account,name:l10n_hr.2_hr_235
#: model:account.account.template,name:l10n_hr.hr_235
msgid "Liabilities due to operations in free-trade zone"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_201
#: model:account.account,name:l10n_hr.2_hr_201
#: model:account.account.template,name:l10n_hr.hr_201
msgid "Liabilities due to share in results"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_246
#: model:account.account,name:l10n_hr.2_hr_246
#: model:account.account.template,name:l10n_hr.hr_246
msgid "Liabilities for Chamber membership fee"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_248
#: model:account.account,name:l10n_hr.2_hr_248
#: model:account.account.template,name:l10n_hr.hr_248
msgid "Liabilities for a Country's and municipal's taxes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_225
#: model:account.account,name:l10n_hr.1_hr_254
#: model:account.account,name:l10n_hr.2_hr_225
#: model:account.account,name:l10n_hr.2_hr_254
#: model:account.account.template,name:l10n_hr.hr_225
#: model:account.account.template,name:l10n_hr.hr_254
msgid "Liabilities for advances received"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_233
#: model:account.account,name:l10n_hr.2_hr_233
#: model:account.account.template,name:l10n_hr.hr_233
msgid "Liabilities for commission or consignment sale of goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_247
#: model:account.account,name:l10n_hr.2_hr_247
#: model:account.account.template,name:l10n_hr.hr_247
msgid "Liabilities for duties and customs duty"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_244
#: model:account.account,name:l10n_hr.2_hr_244
#: model:account.account.template,name:l10n_hr.hr_244
msgid "Liabilities for excise duties and other taxes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_245
#: model:account.account,name:l10n_hr.2_hr_245
#: model:account.account.template,name:l10n_hr.hr_245
msgid "Liabilities for fee to National tourism office"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_242
#: model:account.account,name:l10n_hr.2_hr_242
#: model:account.account.template,name:l10n_hr.hr_242
msgid "Liabilities for insurance contributions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_256
#: model:account.account,name:l10n_hr.2_hr_256
#: model:account.account.template,name:l10n_hr.hr_256
msgid "Liabilities for long term securities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_224
#: model:account.account,name:l10n_hr.2_hr_224
#: model:account.account.template,name:l10n_hr.hr_224
msgid "Liabilities for not invoiced but received goods and services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_249
#: model:account.account,name:l10n_hr.2_hr_249
#: model:account.account.template,name:l10n_hr.hr_249
msgid "Liabilities for other not mentioned taxes, contributions and duties"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_241
#: model:account.account,name:l10n_hr.2_hr_241
#: model:account.account.template,name:l10n_hr.hr_241
msgid "Liabilities for personal income tax and surtax"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_998
#: model:account.account,name:l10n_hr.2_hr_998
#: model:account.account.template,name:l10n_hr.hr_998
msgid "Liabilities for securities in circulation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_997
#: model:account.account,name:l10n_hr.2_hr_997
#: model:account.account.template,name:l10n_hr.hr_997
msgid "Liabilities for securities which are not in circulation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240
#: model:account.account,name:l10n_hr.2_hr_240
#: model:account.account.template,name:l10n_hr.hr_240
msgid "Liabilities for value added taxes"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_217
#: model:account.account,name:l10n_hr.2_hr_217
#: model:account.account.template,name:l10n_hr.hr_217
msgid "Liabilities from discount services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_257
#: model:account.account,name:l10n_hr.2_hr_257
#: model:account.account.template,name:l10n_hr.hr_257
msgid "Liabilities towards associated undertakings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_215
#: model:account.account,name:l10n_hr.1_hr_252
#: model:account.account,name:l10n_hr.2_hr_215
#: model:account.account,name:l10n_hr.2_hr_252
#: model:account.account.template,name:l10n_hr.hr_215
#: model:account.account.template,name:l10n_hr.hr_252
msgid "Liabilities towards banks and other financial institutions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_216
#: model:account.account,name:l10n_hr.2_hr_216
#: model:account.account.template,name:l10n_hr.hr_216
msgid "Liabilities towards credit card institutions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_230
#: model:account.account,name:l10n_hr.2_hr_230
#: model:account.account.template,name:l10n_hr.hr_230
msgid "Liabilities towards employees"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_23
#: model:account.group,name:l10n_hr.2_hr_group_23
#: model:account.group.template,name:l10n_hr.hr_group_23
msgid "Liabilities towards employees and other liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_237
#: model:account.account,name:l10n_hr.2_hr_237
#: model:account.account.template,name:l10n_hr.hr_237
msgid "Liabilities towards foreign business units"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_258
#: model:account.account,name:l10n_hr.2_hr_258
#: model:account.account.template,name:l10n_hr.hr_258
msgid "Liabilities towards government"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_234
#: model:account.account,name:l10n_hr.2_hr_234
#: model:account.account.template,name:l10n_hr.hr_234
msgid "Liabilities towards insurance companies"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24001
#: model:account.account,name:l10n_hr.2_hr_24001
#: model:account.account.template,name:l10n_hr.hr_24001
msgid "Liability for VAT"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2401
#: model:account.account,name:l10n_hr.2_hr_2401
#: model:account.account.template,name:l10n_hr.hr_2401
msgid ""
"Liability for VAT from the transferred tax liability from the home country -"
" 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24003
#: model:account.account,name:l10n_hr.2_hr_24003
#: model:account.account.template,name:l10n_hr.hr_24003
msgid "Liability for VAT on unbilled deliveries"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24005
#: model:account.account,name:l10n_hr.2_hr_24005
#: model:account.account.template,name:l10n_hr.hr_24005
msgid "Liability for corrected VAT due to conversion of goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2407
#: model:account.account,name:l10n_hr.2_hr_2407
#: model:account.account.template,name:l10n_hr.hr_2407
msgid ""
"Liability for the difference between tax and input tax in the taxation "
"period"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_231
#: model:account.account,name:l10n_hr.2_hr_231
#: model:account.account.template,name:l10n_hr.hr_231
msgid "Liability from procurements and grants"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_l10n_hr_chart_template_liquidity_transfer
#: model:account.account,name:l10n_hr.2_l10n_hr_chart_template_liquidity_transfer
#: model:account.account.template,name:l10n_hr.l10n_hr_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_065
#: model:account.account,name:l10n_hr.2_hr_065
#: model:account.account.template,name:l10n_hr.hr_065
msgid "Loans given to third party"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_061
#: model:account.account,name:l10n_hr.2_hr_061
#: model:account.account.template,name:l10n_hr.hr_061
msgid "Loans, deposit etc given to a Group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_063
#: model:account.account,name:l10n_hr.2_hr_063
#: model:account.account.template,name:l10n_hr.hr_063
msgid "Loans, deposit etc given to companies in associated undertakings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_111
#: model:account.account,name:l10n_hr.2_hr_111
#: model:account.account.template,name:l10n_hr.hr_111
msgid "Loans, deposits etc in a Group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_113
#: model:account.account,name:l10n_hr.2_hr_113
#: model:account.account.template,name:l10n_hr.hr_113
msgid "Loans, deposits etc to companies in associated undertakings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_253
#: model:account.account,name:l10n_hr.2_hr_253
#: model:account.account.template,name:l10n_hr.hr_253
msgid "Long term leasing liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_25
#: model:account.group,name:l10n_hr.2_hr_group_25
#: model:account.group.template,name:l10n_hr.hr_group_25
msgid "Long term liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_452
#: model:account.account,name:l10n_hr.2_hr_452
#: model:account.account.template,name:l10n_hr.hr_452
msgid "Long term provision for litigation losses"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_28
#: model:account.group,name:l10n_hr.2_hr_group_28
#: model:account.group.template,name:l10n_hr.hr_group_28
msgid "Long term provisions for risks and charges"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_07
#: model:account.group,name:l10n_hr.2_hr_group_07
#: model:account.group.template,name:l10n_hr.hr_group_07
msgid "Long term receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_072
#: model:account.account,name:l10n_hr.2_hr_072
#: model:account.account.template,name:l10n_hr.hr_072
msgid "Long term trade receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_450
#: model:account.account,name:l10n_hr.2_hr_450
#: model:account.account.template,name:l10n_hr.hr_450
msgid ""
"Long-term provision for pensions, severance payments and other employment "
"benefits"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_453
#: model:account.account,name:l10n_hr.2_hr_453
#: model:account.account.template,name:l10n_hr.hr_453
msgid "Long-term provision for renewal of natural resources"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_451
#: model:account.account,name:l10n_hr.2_hr_451
#: model:account.account.template,name:l10n_hr.hr_451
msgid "Long-term provision for tax obligations"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_476
#: model:account.account,name:l10n_hr.2_hr_476
#: model:account.account.template,name:l10n_hr.hr_476
msgid ""
"Loss from sale of investments in shares, stakes, bonds and other securities "
"(which are sold below cost)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_730
#: model:account.account,name:l10n_hr.2_hr_730
#: model:account.account.template,name:l10n_hr.hr_730
msgid "Loss on sale of property, plant and equipment (HSFI 8 t. 8.35.)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_732
#: model:account.account,name:l10n_hr.2_hr_732
#: model:account.account.template,name:l10n_hr.hr_732
msgid "Losses due to dispossession or natural disasters on material assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_738
#: model:account.account,name:l10n_hr.2_hr_738
#: model:account.account.template,name:l10n_hr.hr_738
msgid "Losses from agricultural assets arising from valuation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_737
#: model:account.account,name:l10n_hr.2_hr_737
#: model:account.account.template,name:l10n_hr.hr_737
msgid "Losses from biological assets arising from valuation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_412
#: model:account.account,name:l10n_hr.2_hr_412
#: model:account.account.template,name:l10n_hr.hr_412
msgid "Maintenance and securities services"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_50
#: model:account.group,name:l10n_hr.1_hr_group_53
#: model:account.group,name:l10n_hr.1_hr_group_72
#: model:account.group,name:l10n_hr.2_hr_group_50
#: model:account.group,name:l10n_hr.2_hr_group_53
#: model:account.group,name:l10n_hr.2_hr_group_72
#: model:account.group.template,name:l10n_hr.hr_group_50
#: model:account.group.template,name:l10n_hr.hr_group_53
#: model:account.group.template,name:l10n_hr.hr_group_72
msgid "Management and administrative overheads"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_648
#: model:account.account,name:l10n_hr.2_hr_648
#: model:account.account.template,name:l10n_hr.hr_648
msgid "Margin included in sale price of finished goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_668
#: model:account.account,name:l10n_hr.2_hr_668
#: model:account.account.template,name:l10n_hr.hr_668
msgid "Margin included in sale price of merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_688
#: model:account.account,name:l10n_hr.2_hr_688
#: model:account.account.template,name:l10n_hr.hr_688
msgid "Margin included in the sale price of property and arts held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_40
#: model:account.group,name:l10n_hr.2_hr_group_40
#: model:account.group.template,name:l10n_hr.hr_group_40
msgid "Material cost"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_311
#: model:account.account,name:l10n_hr.2_hr_311
#: model:account.account.template,name:l10n_hr.hr_311
msgid "Material in processing, finishing and manipulation phases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_312
#: model:account.account,name:l10n_hr.2_hr_312
#: model:account.account.template,name:l10n_hr.hr_312
msgid "Material sent for finishing to partners"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_510
#: model:account.account,name:l10n_hr.2_hr_510
#: model:account.account.template,name:l10n_hr.hr_510
msgid "Material used during selling process in total selling costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_500
#: model:account.account,name:l10n_hr.2_hr_500
#: model:account.account.template,name:l10n_hr.hr_500
msgid "Material used in management and administrative overheads"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_313
#: model:account.account,name:l10n_hr.2_hr_313
#: model:account.account.template,name:l10n_hr.hr_313
msgid "Materials used in agriculture"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_906
#: model:account.account,name:l10n_hr.2_hr_906
#: model:account.account.template,name:l10n_hr.hr_906
msgid "Members business stake in cooperative capital"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_66
#: model:account.group,name:l10n_hr.2_hr_group_66
#: model:account.group.template,name:l10n_hr.hr_group_66
msgid "Merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_662
#: model:account.account,name:l10n_hr.2_hr_662
#: model:account.account.template,name:l10n_hr.hr_662
msgid "Merchandise goods given in commission and consignment sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_661
#: model:account.account,name:l10n_hr.2_hr_661
#: model:account.account.template,name:l10n_hr.hr_661
msgid "Merchandise goods in others warehouse and in storefront"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_663
#: model:account.account,name:l10n_hr.2_hr_663
#: model:account.account.template,name:l10n_hr.hr_663
msgid "Merchandise goods in stores"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_667
#: model:account.account,name:l10n_hr.2_hr_667
#: model:account.account.template,name:l10n_hr.hr_667
msgid "Merchandise goods in transit"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_660
#: model:account.account,name:l10n_hr.2_hr_660
#: model:account.account.template,name:l10n_hr.hr_660
msgid "Merchandise goods on stock"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_665
#: model:account.account,name:l10n_hr.2_hr_665
#: model:account.account.template,name:l10n_hr.hr_665
msgid "Merchandise goods placed in tax-free warehouse or in free-trade area"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_781
#: model:account.account,name:l10n_hr.2_hr_781
#: model:account.account.template,name:l10n_hr.hr_781
msgid "Net gain on disposal of assets, surpluses and evaluation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_960
#: model:account.account,name:l10n_hr.2_hr_960
#: model:account.account.template,name:l10n_hr.hr_960
msgid "Non-controlling interest"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_96
#: model:account.group,name:l10n_hr.2_hr_group_96
#: model:account.group.template,name:l10n_hr.hr_group_96
msgid "Non-controlling interests"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_69
#: model:account.group,name:l10n_hr.2_hr_group_69
#: model:account.group.template,name:l10n_hr.hr_group_69
msgid "Non-current assets held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_06
#: model:account.group,name:l10n_hr.2_hr_group_06
#: model:account.group.template,name:l10n_hr.hr_group_06
msgid "Non-current financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_033
#: model:account.account,name:l10n_hr.2_hr_033
#: model:account.account.template,name:l10n_hr.hr_033
msgid "Not deductible VAT prepayments (for cars, purchases after 1.1.2018. )"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240011
#: model:account.account,name:l10n_hr.2_hr_240011
#: model:account.account.template,name:l10n_hr.hr_240011
msgid "Obligation for VAT - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240012
#: model:account.account,name:l10n_hr.2_hr_240012
#: model:account.account.template,name:l10n_hr.hr_240012
msgid "Obligation for VAT - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240010
#: model:account.account,name:l10n_hr.2_hr_240010
#: model:account.account.template,name:l10n_hr.hr_240010
msgid "Obligation for VAT - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2405
#: model:account.account,name:l10n_hr.2_hr_2405
#: model:account.account.template,name:l10n_hr.hr_2405
msgid "Obligation for calculated VAT on import"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2511
#: model:account.account,name:l10n_hr.2_hr_2511
#: model:account.account.template,name:l10n_hr.hr_2511
msgid "Obligations for loans, deposits and the like interest"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2510
#: model:account.account,name:l10n_hr.2_hr_2510
#: model:account.account.template,name:l10n_hr.hr_2510
msgid ""
"Obligations for loans, deposits and the like of companies connected by a "
"participating"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2131
#: model:account.account,name:l10n_hr.2_hr_2131
#: model:account.account.template,name:l10n_hr.hr_2131
msgid ""
"Obligations for loans, deposits and the like of companies connected by a "
"participating interest"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2001
#: model:account.account,name:l10n_hr.1_hr_2501
#: model:account.account,name:l10n_hr.2_hr_2001
#: model:account.account,name:l10n_hr.2_hr_2501
#: model:account.account.template,name:l10n_hr.hr_2001
#: model:account.account.template,name:l10n_hr.hr_2501
msgid ""
"Obligations for loans, deposits and the like of entrepreneurs within the "
"group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2000
#: model:account.account,name:l10n_hr.1_hr_2500
#: model:account.account,name:l10n_hr.2_hr_2000
#: model:account.account,name:l10n_hr.2_hr_2500
#: model:account.account.template,name:l10n_hr.hr_2000
#: model:account.account.template,name:l10n_hr.hr_2500
msgid "Obligations towards Entrepreneurs within the Group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2130
#: model:account.account,name:l10n_hr.2_hr_2130
#: model:account.account.template,name:l10n_hr.hr_2130
msgid "Obligations towards companies connected by participating interests"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_636
#: model:account.account,name:l10n_hr.2_hr_636
#: model:account.account.template,name:l10n_hr.hr_636
msgid "Obsolete and slow moving inventory"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_99
#: model:account.group,name:l10n_hr.2_hr_group_99
#: model:account.group.template,name:l10n_hr.hr_group_99
msgid "Off-balance sheet balance"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_684
#: model:account.account,name:l10n_hr.2_hr_684
#: model:account.account.template,name:l10n_hr.hr_684
msgid "Old timers for trading"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_933
#: model:account.account,name:l10n_hr.2_hr_933
#: model:account.account.template,name:l10n_hr.hr_933
msgid "Other accumulated comprehensive income / loss"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_785
#: model:account.account,name:l10n_hr.2_hr_785
#: model:account.account.template,name:l10n_hr.hr_785
msgid "Other business income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_108
#: model:account.account,name:l10n_hr.2_hr_108
#: model:account.account.template,name:l10n_hr.hr_108
msgid "Other cash"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_935
#: model:account.account,name:l10n_hr.2_hr_935
#: model:account.account.template,name:l10n_hr.hr_935
msgid "Other changes in capital (minorities)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_472
#: model:account.account,name:l10n_hr.2_hr_472
#: model:account.account.template,name:l10n_hr.hr_472
msgid "Other costs from a Group companies"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_505
#: model:account.account,name:l10n_hr.2_hr_505
#: model:account.account.template,name:l10n_hr.hr_505
msgid "Other costs related to the administration and selling"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_515
#: model:account.account,name:l10n_hr.2_hr_515
#: model:account.account.template,name:l10n_hr.hr_515
msgid ""
"Other costs related to the deliverables, invoicing and marketing, packaging,"
" export"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_489
#: model:account.account,name:l10n_hr.2_hr_489
#: model:account.account.template,name:l10n_hr.hr_489
msgid "Other costs-expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_239
#: model:account.account,name:l10n_hr.2_hr_239
#: model:account.account.template,name:l10n_hr.hr_239
msgid "Other current liability"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_128
#: model:account.account,name:l10n_hr.2_hr_128
#: model:account.account.template,name:l10n_hr.hr_128
msgid "Other current receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_081
#: model:account.account,name:l10n_hr.2_hr_081
#: model:account.account.template,name:l10n_hr.hr_081
msgid "Other deferred tax asset"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_other_exempt_O
#: model:account.tax,description:l10n_hr.2_VAT_S_other_exempt_O
#: model:account.tax.template,description:l10n_hr.VAT_S_other_exempt_O
msgid "Other exempt"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_734
#: model:account.account,name:l10n_hr.2_hr_734
#: model:account.account.template,name:l10n_hr.hr_734
msgid ""
"Other expense due to penalties, paid compensations and liabilities from "
"subsequent events"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_48
#: model:account.group,name:l10n_hr.1_hr_group_73
#: model:account.group,name:l10n_hr.2_hr_group_48
#: model:account.group,name:l10n_hr.2_hr_group_73
#: model:account.group.template,name:l10n_hr.hr_group_48
#: model:account.group.template,name:l10n_hr.hr_group_73
msgid "Other expenses"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_419
#: model:account.account,name:l10n_hr.2_hr_419
#: model:account.account.template,name:l10n_hr.hr_419
msgid "Other external costs"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_41
#: model:account.group,name:l10n_hr.2_hr_group_41
#: model:account.group.template,name:l10n_hr.hr_group_41
msgid "Other external costs (cost of services)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_117
#: model:account.account,name:l10n_hr.2_hr_117
#: model:account.account.template,name:l10n_hr.hr_117
msgid "Other financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_479
#: model:account.account,name:l10n_hr.2_hr_479
#: model:account.account.template,name:l10n_hr.hr_479
msgid "Other financial expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_779
#: model:account.account,name:l10n_hr.2_hr_779
#: model:account.account.template,name:l10n_hr.hr_779
msgid "Other financial income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_789
#: model:account.account,name:l10n_hr.2_hr_789
#: model:account.account.template,name:l10n_hr.hr_789
msgid "Other income (free deliverables, non-operational income)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7702
#: model:account.account,name:l10n_hr.2_hr_7702
#: model:account.account.template,name:l10n_hr.hr_7702
msgid ""
"Other income based on interest from relations with entrepreneurs within the "
"group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_758
#: model:account.account,name:l10n_hr.2_hr_758
#: model:account.account.template,name:l10n_hr.hr_758
msgid "Other income from other sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_768
#: model:account.account,name:l10n_hr.2_hr_768
#: model:account.account.template,name:l10n_hr.hr_768
msgid "Other income from sale of goods and rendering of merchandise services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_014
#: model:account.account,name:l10n_hr.2_hr_014
#: model:account.account.template,name:l10n_hr.hr_014
msgid "Other intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_301
#: model:account.account,name:l10n_hr.2_hr_301
#: model:account.account.template,name:l10n_hr.hr_301
msgid "Other inventory dependant costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_259
#: model:account.account,name:l10n_hr.2_hr_259
#: model:account.account.template,name:l10n_hr.hr_259
msgid "Other long term liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_067
#: model:account.account,name:l10n_hr.2_hr_067
#: model:account.account.template,name:l10n_hr.hr_067
msgid "Other long-term financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_077
#: model:account.account,name:l10n_hr.2_hr_077
#: model:account.account.template,name:l10n_hr.hr_077
msgid "Other long-term receivables"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_46
#: model:account.group,name:l10n_hr.2_hr_group_46
#: model:account.group.template,name:l10n_hr.hr_group_46
msgid "Other operating costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_469
#: model:account.account,name:l10n_hr.2_hr_469
#: model:account.account.template,name:l10n_hr.hr_469
msgid "Other operating costs- intangible"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_721
#: model:account.account,name:l10n_hr.2_hr_721
#: model:account.account.template,name:l10n_hr.hr_721
msgid "Other operational expense"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_78
#: model:account.group,name:l10n_hr.2_hr_group_78
#: model:account.group.template,name:l10n_hr.hr_group_78
msgid "Other operational income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_199
#: model:account.account,name:l10n_hr.2_hr_199
#: model:account.account.template,name:l10n_hr.hr_199
msgid "Other prepaid expense"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_035
#: model:account.account,name:l10n_hr.2_hr_035
#: model:account.account.template,name:l10n_hr.hr_035
msgid "Other property, plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_691
#: model:account.account,name:l10n_hr.2_hr_691
#: model:account.account.template,name:l10n_hr.hr_691
msgid "Other property, plant and equipment held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_285
#: model:account.account,name:l10n_hr.2_hr_285
#: model:account.account.template,name:l10n_hr.hr_285
msgid "Other provisions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_138
#: model:account.account,name:l10n_hr.2_hr_138
#: model:account.account.template,name:l10n_hr.hr_138
msgid "Other receivables from operating activities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_155
#: model:account.account,name:l10n_hr.2_hr_155
#: model:account.account.template,name:l10n_hr.hr_155
msgid "Other receivables from the Government"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_924
#: model:account.account,name:l10n_hr.2_hr_924
#: model:account.account.template,name:l10n_hr.hr_924
msgid "Other reserves"
msgstr ""

#. module: l10n_hr
#: model:account.tax.group,name:l10n_hr.tax_group_pdv_0
msgid "PDV 0%"
msgstr ""

#. module: l10n_hr
#: model:account.tax.group,name:l10n_hr.tax_group_pdv_13
msgid "PDV 13%"
msgstr ""

#. module: l10n_hr
#: model:account.tax.group,name:l10n_hr.tax_group_pdv_25
msgid "PDV 25%"
msgstr ""

#. module: l10n_hr
#: model:account.tax.group,name:l10n_hr.tax_group_pdv_5
msgid "PDV 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_403
#: model:account.account,name:l10n_hr.2_hr_403
#: model:account.account.template,name:l10n_hr.hr_403
msgid "Packaging cost"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_361
#: model:account.account,name:l10n_hr.2_hr_361
#: model:account.account.template,name:l10n_hr.hr_361
msgid "Packaging in use"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_351
#: model:account.account,name:l10n_hr.2_hr_351
#: model:account.account.template,name:l10n_hr.hr_351
msgid "Packaging on stock"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_364
#: model:account.account,name:l10n_hr.2_hr_364
#: model:account.account.template,name:l10n_hr.hr_364
msgid "Packaging write off"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_910
#: model:account.account,name:l10n_hr.2_hr_910
#: model:account.account.template,name:l10n_hr.hr_910
msgid "Paid in stake/share above issued share capital"
msgstr ""

#. module: l10n_hr
#: model:account.fiscal.position,name:l10n_hr.1_fiscal_position_hr_eu_out
#: model:account.fiscal.position,name:l10n_hr.2_fiscal_position_hr_eu_out
#: model:account.fiscal.position.template,name:l10n_hr.fiscal_position_hr_eu_out
msgid "Partner outside the EU"
msgstr ""

#. module: l10n_hr
#: model:account.fiscal.position,name:l10n_hr.1_fiscal_position_hr_person_private
#: model:account.fiscal.position,name:l10n_hr.2_fiscal_position_hr_person_private
#: model:account.fiscal.position.template,name:l10n_hr.fiscal_position_hr_person_private
msgid "Partner private"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_904
#: model:account.account,name:l10n_hr.2_hr_904
#: model:account.account.template,name:l10n_hr.hr_904
msgid "Partners business stakes in limited partnership"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_300
#: model:account.account,name:l10n_hr.2_hr_300
#: model:account.account.template,name:l10n_hr.hr_300
msgid "Payable purchase price"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_484
#: model:account.account,name:l10n_hr.2_hr_484
#: model:account.account.template,name:l10n_hr.hr_484
msgid ""
"Penalties, compensation for damages occurred and contract related costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_474
#: model:account.account,name:l10n_hr.2_hr_474
#: model:account.account.template,name:l10n_hr.hr_474
msgid "Penalty interest"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_030
#: model:account.account,name:l10n_hr.2_hr_030
#: model:account.account.template,name:l10n_hr.hr_030
msgid "Plant"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_037
#: model:account.account,name:l10n_hr.2_hr_037
#: model:account.account.template,name:l10n_hr.hr_037
msgid "Plant and equipment under construction"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_03
#: model:account.group,name:l10n_hr.2_hr_group_03
#: model:account.group.template,name:l10n_hr.hr_group_03
msgid "Plant, equipment, tools and transportation vehicles"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_190
#: model:account.account,name:l10n_hr.1_hr_192
#: model:account.account,name:l10n_hr.2_hr_190
#: model:account.account,name:l10n_hr.2_hr_192
#: model:account.account.template,name:l10n_hr.hr_190
#: model:account.account.template,name:l10n_hr.hr_192
msgid "Prepaid expense and accrued income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_194
#: model:account.account,name:l10n_hr.2_hr_194
#: model:account.account.template,name:l10n_hr.hr_194
msgid "Prepaid expense for concession rights"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_195
#: model:account.account,name:l10n_hr.2_hr_195
#: model:account.account.template,name:l10n_hr.hr_195
msgid "Prepaid expense for franchise fee"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_196
#: model:account.account,name:l10n_hr.2_hr_196
#: model:account.account.template,name:l10n_hr.hr_196
msgid "Prepaid expense for licences and patents"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_19
#: model:account.group,name:l10n_hr.2_hr_group_19
#: model:account.group.template,name:l10n_hr.hr_group_19
msgid "Prepaid expenses and accrued income"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_602
#: model:account.account,name:l10n_hr.2_hr_602
#: model:account.account.template,name:l10n_hr.hr_602
msgid "Production in cooperation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_606
#: model:account.account,name:l10n_hr.2_hr_606
#: model:account.account.template,name:l10n_hr.hr_606
msgid "Production in processing and manipulation phases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_605
#: model:account.account,name:l10n_hr.2_hr_605
#: model:account.account.template,name:l10n_hr.hr_605
msgid "Production process in free trade zone"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_80
#: model:account.group,name:l10n_hr.1_hr_group_95
#: model:account.group,name:l10n_hr.2_hr_group_80
#: model:account.group,name:l10n_hr.2_hr_group_95
#: model:account.group.template,name:l10n_hr.hr_group_80
#: model:account.group.template,name:l10n_hr.hr_group_95
msgid "Profit or loss for the year"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_83
#: model:account.group,name:l10n_hr.2_hr_group_83
#: model:account.group.template,name:l10n_hr.hr_group_83
msgid "Profit or loss for the year attributable to others"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_81
#: model:account.group,name:l10n_hr.2_hr_group_81
#: model:account.group.template,name:l10n_hr.hr_group_81
msgid ""
"Profit or loss from discontinued operations (applicable for companies which "
"use msfi and have discontinued operations)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_415
#: model:account.account,name:l10n_hr.2_hr_415
#: model:account.account.template,name:l10n_hr.hr_415
msgid "Promotion , sponsorship and fairs costs"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_68
#: model:account.group,name:l10n_hr.2_hr_group_68
#: model:account.group.template,name:l10n_hr.hr_group_68
msgid "Properties and arts held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_990
#: model:account.account,name:l10n_hr.2_hr_990
#: model:account.account.template,name:l10n_hr.hr_990
msgid "Property plant and equipment in use"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_683
#: model:account.account,name:l10n_hr.2_hr_683
#: model:account.account.template,name:l10n_hr.hr_683
msgid "Property which is classified as held for sale"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_680
#: model:account.account,name:l10n_hr.2_hr_680
#: model:account.account.template,name:l10n_hr.hr_680
msgid "Property which is classified as held for sale- at cost"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_280
#: model:account.account,name:l10n_hr.2_hr_280
#: model:account.account.template,name:l10n_hr.hr_280
msgid "Provision for employee benefits"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_284
#: model:account.account,name:l10n_hr.2_hr_284
#: model:account.account.template,name:l10n_hr.hr_284
msgid "Provision for risks within warranty period"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_281
#: model:account.account,name:l10n_hr.2_hr_281
#: model:account.account.template,name:l10n_hr.hr_281
msgid "Provision for tax liabilities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_299
#: model:account.account,name:l10n_hr.2_hr_299
#: model:account.account.template,name:l10n_hr.hr_299
msgid "Provision for unused vacation days"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_45
#: model:account.group,name:l10n_hr.2_hr_group_45
#: model:account.group.template,name:l10n_hr.hr_group_45
msgid "Provisions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_282
#: model:account.account,name:l10n_hr.2_hr_282
#: model:account.account.template,name:l10n_hr.hr_282
msgid "Provisions for legal cases"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_283
#: model:account.account,name:l10n_hr.2_hr_283
#: model:account.account.template,name:l10n_hr.hr_283
msgid "Provisions for renewal of natural resources"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_IN_ROC_13
#: model:account.tax,description:l10n_hr.2_VAT_P_IN_ROC_13
#: model:account.tax.template,description:l10n_hr.VAT_P_IN_ROC_13
msgid "Purchase 13% in country"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_NOT_IN_EU_13
#: model:account.tax,description:l10n_hr.2_VAT_P_NOT_IN_EU_13
#: model:account.tax.template,description:l10n_hr.VAT_P_NOT_IN_EU_13
msgid "Purchase 13% not in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_IN_ROC_25
#: model:account.tax,description:l10n_hr.2_VAT_P_IN_ROC_25
#: model:account.tax.template,description:l10n_hr.VAT_P_IN_ROC_25
msgid "Purchase 25% in country"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_NOT_IN_EU_25
#: model:account.tax,description:l10n_hr.2_VAT_P_NOT_IN_EU_25
#: model:account.tax.template,description:l10n_hr.VAT_P_NOT_IN_EU_25
msgid "Purchase 25% not in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_IN_ROC_5
#: model:account.tax,description:l10n_hr.2_VAT_P_IN_ROC_5
#: model:account.tax.template,description:l10n_hr.VAT_P_IN_ROC_5
msgid "Purchase 5% in country"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_NOT_IN_EU_5
#: model:account.tax,description:l10n_hr.2_VAT_P_NOT_IN_EU_5
#: model:account.tax.template,description:l10n_hr.VAT_P_NOT_IN_EU_5
msgid "Purchase 5% not in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_G_IN_EU_13
#: model:account.tax,description:l10n_hr.2_VAT_P_G_IN_EU_13
#: model:account.tax.template,description:l10n_hr.VAT_P_G_IN_EU_13
msgid "Purchase goods 13% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_G_IN_EU_25
#: model:account.tax,description:l10n_hr.2_VAT_P_G_IN_EU_25
#: model:account.tax.template,description:l10n_hr.VAT_P_G_IN_EU_25
msgid "Purchase goods 25% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_G_IN_EU_5
#: model:account.tax,description:l10n_hr.2_VAT_P_G_IN_EU_5
#: model:account.tax.template,description:l10n_hr.VAT_P_G_IN_EU_5
msgid "Purchase goods 5% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_S_IN_EU_13
#: model:account.tax,description:l10n_hr.2_VAT_P_S_IN_EU_13
#: model:account.tax.template,description:l10n_hr.VAT_P_S_IN_EU_13
msgid "Purchase service 13% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_S_IN_EU_25
#: model:account.tax,description:l10n_hr.2_VAT_P_S_IN_EU_25
#: model:account.tax.template,description:l10n_hr.VAT_P_S_IN_EU_25
msgid "Purchase service 25% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_P_S_IN_EU_5
#: model:account.tax,description:l10n_hr.2_VAT_P_S_IN_EU_5
#: model:account.tax.template,description:l10n_hr.VAT_P_S_IN_EU_5
msgid "Purchase service 5% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.chart.template,name:l10n_hr.l10n_hr_chart_template
msgid "RRIF account plan for entrepreneurs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_310
#: model:account.account,name:l10n_hr.2_hr_310
#: model:account.account.template,name:l10n_hr.hr_310
msgid "Raw material on stock"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_31
#: model:account.group,name:l10n_hr.2_hr_group_31
#: model:account.group.template,name:l10n_hr.hr_group_31
msgid "Raw materials"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_3
#: model:account.group,name:l10n_hr.2_hr_group_3
#: model:account.group.template,name:l10n_hr.hr_group_3
msgid "Raw materials, materials, spare parts and small inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_071
#: model:account.account,name:l10n_hr.2_hr_071
#: model:account.account.template,name:l10n_hr.hr_071
msgid "Receivable from associated undertakings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_148
#: model:account.account,name:l10n_hr.2_hr_148
#: model:account.account.template,name:l10n_hr.hr_148
msgid "Receivables for Country and municipal tax"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_146
#: model:account.account,name:l10n_hr.2_hr_146
#: model:account.account.template,name:l10n_hr.hr_146
msgid "Receivables for Croatian Economy Chamber or similar Chambers"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_154
#: model:account.account,name:l10n_hr.2_hr_154
#: model:account.account.template,name:l10n_hr.hr_154
msgid "Receivables for Fund for packaging repurchase"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_145
#: model:account.account,name:l10n_hr.2_hr_145
#: model:account.account.template,name:l10n_hr.hr_145
msgid "Receivables for National tourism office fee"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_151
#: model:account.account,name:l10n_hr.2_hr_151
#: model:account.account.template,name:l10n_hr.hr_151
msgid "Receivables for Pension insurance institute"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_074
#: model:account.account,name:l10n_hr.2_hr_074
#: model:account.account.template,name:l10n_hr.hr_074
msgid "Receivables for a given guarantees"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_076
#: model:account.account,name:l10n_hr.1_hr_125
#: model:account.account,name:l10n_hr.2_hr_076
#: model:account.account,name:l10n_hr.2_hr_125
#: model:account.account.template,name:l10n_hr.hr_076
#: model:account.account.template,name:l10n_hr.hr_125
msgid "Receivables for advance payments for services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_135
#: model:account.account,name:l10n_hr.2_hr_135
#: model:account.account.template,name:l10n_hr.hr_135
msgid "Receivables for assets held on free-trade zone"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_153
#: model:account.account,name:l10n_hr.2_hr_153
#: model:account.account.template,name:l10n_hr.hr_153
msgid "Receivables for bonuses, premiums and government grants"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_143
#: model:account.account,name:l10n_hr.2_hr_143
#: model:account.account.template,name:l10n_hr.hr_143
msgid "Receivables for corporate income tax and withholding tax"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_147
#: model:account.account,name:l10n_hr.2_hr_147
#: model:account.account.template,name:l10n_hr.hr_147
msgid "Receivables for duties and prepayments to customs duty"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_144
#: model:account.account,name:l10n_hr.2_hr_144
#: model:account.account.template,name:l10n_hr.hr_144
msgid "Receivables for excise duties and other taxes"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_0
#: model:account.group,name:l10n_hr.2_hr_group_0
#: model:account.group.template,name:l10n_hr.hr_group_0
msgid "Receivables for issued capital and non-current assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_075
#: model:account.account,name:l10n_hr.2_hr_075
#: model:account.account.template,name:l10n_hr.hr_075
msgid ""
"Receivables for legal cases in company's favour and other risk receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_156
#: model:account.account,name:l10n_hr.2_hr_156
#: model:account.account.template,name:l10n_hr.hr_156
msgid "Receivables for more paid fees for disabled person"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_004
#: model:account.account,name:l10n_hr.2_hr_004
#: model:account.account.template,name:l10n_hr.hr_004
msgid "Receivables for other business stakes in capital"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_149
#: model:account.account,name:l10n_hr.2_hr_149
#: model:account.account.template,name:l10n_hr.hr_149
msgid "Receivables for other not mentioned taxes, contributions and duties"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_142
#: model:account.account,name:l10n_hr.2_hr_142
#: model:account.account.template,name:l10n_hr.hr_142
msgid ""
"Receivables for prepayments made regarding mandatory contributions on "
"personal income tax"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_000
#: model:account.account,name:l10n_hr.2_hr_000
#: model:account.account.template,name:l10n_hr.hr_000
msgid "Receivables for recoded but not paid in capital in joint-stock company"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_002
#: model:account.account,name:l10n_hr.2_hr_002
#: model:account.account.template,name:l10n_hr.hr_002
msgid ""
"Receivables for recoded but not paid in capital in private limited "
"company(analytics by owners)"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_00
#: model:account.group,name:l10n_hr.2_hr_group_00
#: model:account.group.template,name:l10n_hr.hr_group_00
msgid ""
"Receivables for recorded but not paid in capital and non-current financial "
"assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_001
#: model:account.account,name:l10n_hr.2_hr_001
#: model:account.account.template,name:l10n_hr.hr_001
msgid ""
"Receivables for repeated share issues for recorded but not paid capital"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_003
#: model:account.account,name:l10n_hr.2_hr_003
#: model:account.account.template,name:l10n_hr.hr_003
msgid "Receivables for stake in capital of limited partnership"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_079
#: model:account.account,name:l10n_hr.2_hr_079
#: model:account.account.template,name:l10n_hr.hr_079
msgid "Receivables for unrealized interests in loans"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_150
#: model:account.account,name:l10n_hr.2_hr_150
#: model:account.account.template,name:l10n_hr.hr_150
msgid ""
"Receivables from Croatian institute for health insurance regarding "
"employee's sick leave payments"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_136
#: model:account.account,name:l10n_hr.2_hr_136
#: model:account.account.template,name:l10n_hr.hr_136
msgid "Receivables from banks based on customer's loans"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_130
#: model:account.account,name:l10n_hr.2_hr_130
#: model:account.account.template,name:l10n_hr.hr_130
msgid "Receivables from employees"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_13
#: model:account.group,name:l10n_hr.2_hr_group_13
#: model:account.group.template,name:l10n_hr.hr_group_13
msgid "Receivables from employees and other receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_133
#: model:account.account,name:l10n_hr.2_hr_133
#: model:account.account.template,name:l10n_hr.hr_133
msgid "Receivables from equity owners"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_073
#: model:account.account,name:l10n_hr.2_hr_073
#: model:account.account.template,name:l10n_hr.hr_073
msgid "Receivables from factoring"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_137
#: model:account.account,name:l10n_hr.2_hr_137
#: model:account.account.template,name:l10n_hr.hr_137
msgid "Receivables from foreign business units"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_15
#: model:account.group,name:l10n_hr.2_hr_group_15
#: model:account.group.template,name:l10n_hr.hr_group_15
msgid "Receivables from government and other institutions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_116
#: model:account.account,name:l10n_hr.2_hr_116
#: model:account.account.template,name:l10n_hr.hr_116
msgid "Receivables from investments funds"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_152
#: model:account.account,name:l10n_hr.2_hr_152
#: model:account.account.template,name:l10n_hr.hr_152
msgid "Receivables from local government"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_127
#: model:account.account,name:l10n_hr.2_hr_127
#: model:account.account.template,name:l10n_hr.hr_127
msgid "Receivables from other activities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_141
#: model:account.account,name:l10n_hr.2_hr_141
#: model:account.account.template,name:l10n_hr.hr_141
msgid "Receivables from tax and surtax regarding salaries and other incomes"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_14
#: model:account.group,name:l10n_hr.2_hr_group_14
#: model:account.group.template,name:l10n_hr.hr_group_14
msgid "Receivables from the government for taxes, duties and contributions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_131
#: model:account.account,name:l10n_hr.2_hr_131
#: model:account.account.template,name:l10n_hr.hr_131
msgid "Receivables from third party"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_070
#: model:account.account,name:l10n_hr.2_hr_070
#: model:account.account.template,name:l10n_hr.hr_070
msgid "Receivables in a Group"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_118
#: model:account.account,name:l10n_hr.2_hr_118
#: model:account.account.template,name:l10n_hr.hr_118
msgid ""
"Receivables in dispute (e.g.. disputed, in bankruptcy etc, from financial "
"assets)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_134
#: model:account.account,name:l10n_hr.2_hr_134
#: model:account.account.template,name:l10n_hr.hr_134
msgid "Receivables in dispute and risky receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_513
#: model:account.account,name:l10n_hr.2_hr_513
#: model:account.account.template,name:l10n_hr.hr_513
msgid "Rents in the cost of sales"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_418
#: model:account.account,name:l10n_hr.2_hr_418
#: model:account.account.template,name:l10n_hr.hr_418
msgid "Representation costs - hosting"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_010
#: model:account.account,name:l10n_hr.2_hr_010
#: model:account.account.template,name:l10n_hr.hr_010
msgid "Research and development"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_402
#: model:account.account,name:l10n_hr.2_hr_402
#: model:account.account.template,name:l10n_hr.hr_402
msgid "Research and development cost"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_921
#: model:account.account,name:l10n_hr.2_hr_921
#: model:account.account.template,name:l10n_hr.hr_921
msgid "Reserves for treasury shares"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_92
#: model:account.group,name:l10n_hr.2_hr_group_92
#: model:account.group.template,name:l10n_hr.hr_group_92
msgid "Reserves from retained earnings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_940
#: model:account.account,name:l10n_hr.2_hr_940
#: model:account.account.template,name:l10n_hr.hr_940
msgid "Retained earnings"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_94
#: model:account.group,name:l10n_hr.2_hr_group_94
#: model:account.group.template,name:l10n_hr.hr_group_94
msgid "Retained earnings or accumulated loss"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2408
#: model:account.account,name:l10n_hr.2_hr_2408
#: model:account.account.template,name:l10n_hr.hr_2408
msgid "Return of VAT from passenger traffic to natural persons from abroad"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_93
#: model:account.group,name:l10n_hr.2_hr_group_93
#: model:account.group.template,name:l10n_hr.hr_group_93
msgid "Revaluation reserves and fair value reserves"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_930
#: model:account.account,name:l10n_hr.2_hr_930
#: model:account.account.template,name:l10n_hr.hr_930
msgid "Revaluation reserves from tangible and intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_756
#: model:account.account,name:l10n_hr.2_hr_756
#: model:account.account.template,name:l10n_hr.hr_756
msgid "Revenue from leases and rents"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_760
#: model:account.account,name:l10n_hr.2_hr_760
#: model:account.account.template,name:l10n_hr.hr_760
msgid "Revenue from sale of goods"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_75
#: model:account.group,name:l10n_hr.2_hr_group_75
#: model:account.group.template,name:l10n_hr.hr_group_75
msgid "Revenue from sale of goods and rendering of services"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_76
#: model:account.group,name:l10n_hr.2_hr_group_76
#: model:account.group.template,name:l10n_hr.hr_group_76
msgid "Revenue from sale of merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_752
#: model:account.account,name:l10n_hr.2_hr_752
#: model:account.account.template,name:l10n_hr.hr_752
msgid ""
"Revenue from the rendering of construction services- construction services "
"agreement"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_762
#: model:account.account,name:l10n_hr.2_hr_762
#: model:account.account.template,name:l10n_hr.hr_762
msgid "Revenue from the rendering of merchandise services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_751
#: model:account.account,name:l10n_hr.2_hr_751
#: model:account.account.template,name:l10n_hr.hr_751
msgid "Revenue from the rendering of services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_757
#: model:account.account,name:l10n_hr.2_hr_757
#: model:account.account.template,name:l10n_hr.hr_757
msgid "Revenues form partnership agreement"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_788
#: model:account.account,name:l10n_hr.2_hr_788
#: model:account.account.template,name:l10n_hr.hr_788
msgid "Revenues from the sale of a significant part of the assets"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_reverse_O
#: model:account.tax,description:l10n_hr.2_VAT_S_reverse_O
#: model:account.tax.template,description:l10n_hr.VAT_S_reverse_O
msgid "Reverse charge 0%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_991
#: model:account.account,name:l10n_hr.2_hr_991
#: model:account.account.template,name:l10n_hr.hr_991
msgid "Rights and receivables to be used"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_021
#: model:account.account,name:l10n_hr.2_hr_021
#: model:account.account.template,name:l10n_hr.hr_021
msgid "Rights to use land"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_EX_O
#: model:account.tax,description:l10n_hr.2_VAT_S_EX_O
#: model:account.tax.template,description:l10n_hr.VAT_S_EX_O
msgid "Sale 0% not in EU"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_IN_ROC_13
#: model:account.tax,description:l10n_hr.2_VAT_S_IN_ROC_13
#: model:account.tax.template,description:l10n_hr.VAT_S_IN_ROC_13
msgid "Sale 13% in country"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_IN_ROC_25
#: model:account.tax,description:l10n_hr.2_VAT_S_IN_ROC_25
#: model:account.tax.template,description:l10n_hr.VAT_S_IN_ROC_25
msgid "Sale 25% in country"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_IN_ROC_5
#: model:account.tax,description:l10n_hr.2_VAT_S_IN_ROC_5
#: model:account.tax.template,description:l10n_hr.VAT_S_IN_ROC_5
msgid "Sale 5% in country"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_EU_G
#: model:account.tax,description:l10n_hr.2_VAT_S_EU_G
#: model:account.tax.template,description:l10n_hr.VAT_S_EU_G
msgid "Sale goods 0% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_759
#: model:account.account,name:l10n_hr.1_hr_769
#: model:account.account,name:l10n_hr.2_hr_759
#: model:account.account,name:l10n_hr.2_hr_769
#: model:account.account.template,name:l10n_hr.hr_759
#: model:account.account.template,name:l10n_hr.hr_769
msgid "Sale income from Group companies"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_755
#: model:account.account,name:l10n_hr.2_hr_755
#: model:account.account.template,name:l10n_hr.hr_755
msgid "Sale income from internal usage of own goods and services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_765
#: model:account.account,name:l10n_hr.2_hr_765
#: model:account.account.template,name:l10n_hr.hr_765
msgid "Sale income from internal usage of own merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_750
#: model:account.account,name:l10n_hr.2_hr_750
#: model:account.account.template,name:l10n_hr.hr_750
msgid "Sale of goods (analytics by goods or profit centres)"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_EU_S
#: model:account.tax,description:l10n_hr.2_VAT_S_EU_S
#: model:account.tax.template,description:l10n_hr.VAT_S_EU_S
msgid "Sale service 0% in EU"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_753
#: model:account.account,name:l10n_hr.1_hr_761
#: model:account.account,name:l10n_hr.2_hr_753
#: model:account.account,name:l10n_hr.2_hr_761
#: model:account.account.template,name:l10n_hr.hr_753
#: model:account.account.template,name:l10n_hr.hr_761
msgid "Sales income from goods- foreign"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_754
#: model:account.account,name:l10n_hr.2_hr_754
#: model:account.account.template,name:l10n_hr.hr_754
msgid "Sales income from services - foreign"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_992
#: model:account.account,name:l10n_hr.2_hr_992
#: model:account.account.template,name:l10n_hr.hr_992
msgid "Securities"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_993
#: model:account.account,name:l10n_hr.2_hr_993
#: model:account.account.template,name:l10n_hr.hr_993
msgid "Securities in circulation"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_51
#: model:account.group,name:l10n_hr.1_hr_group_54
#: model:account.group,name:l10n_hr.2_hr_group_51
#: model:account.group,name:l10n_hr.2_hr_group_54
#: model:account.group.template,name:l10n_hr.hr_group_51
#: model:account.group.template,name:l10n_hr.hr_group_54
msgid "Selling costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_610
#: model:account.account,name:l10n_hr.2_hr_610
#: model:account.account.template,name:l10n_hr.hr_610
msgid "Semi-manufacture goods on stock (analytics by the production stage)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_462
#: model:account.account,name:l10n_hr.2_hr_462
#: model:account.account.template,name:l10n_hr.hr_462
msgid ""
"Severance payments, gifts, performance awards, grants, insurance premiums "
"and jubilee awards"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_90
#: model:account.group,name:l10n_hr.2_hr_group_90
#: model:account.group.template,name:l10n_hr.hr_group_90
msgid "Share capital"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7401
#: model:account.account,name:l10n_hr.2_hr_7401
#: model:account.account.template,name:l10n_hr.hr_7401
msgid "Share in joint profits undertaking"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_74
#: model:account.group,name:l10n_hr.2_hr_group_74
#: model:account.group.template,name:l10n_hr.hr_group_74
msgid "Share in profit/loss of a group and in associated undertaking"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7450
#: model:account.account,name:l10n_hr.2_hr_7450
#: model:account.account.template,name:l10n_hr.hr_7450
msgid "Share in the loss from the company connected by participating interest"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7400
#: model:account.account,name:l10n_hr.2_hr_7400
#: model:account.account.template,name:l10n_hr.hr_7400
msgid ""
"Share in the profit from the company connected by participating interest"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_7451
#: model:account.account,name:l10n_hr.2_hr_7451
#: model:account.account.template,name:l10n_hr.hr_7451
msgid "Share of loss from joint undertaking"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_903
#: model:account.account,name:l10n_hr.2_hr_903
#: model:account.account.template,name:l10n_hr.hr_903
msgid "Shareholders share in public limited company capital"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_360
#: model:account.account,name:l10n_hr.2_hr_360
#: model:account.account.template,name:l10n_hr.hr_360
msgid "Small inventory in use"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_350
#: model:account.account,name:l10n_hr.2_hr_350
#: model:account.account.template,name:l10n_hr.hr_350
msgid "Small inventory on stock"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_35
#: model:account.group,name:l10n_hr.2_hr_group_35
#: model:account.group.template,name:l10n_hr.hr_group_35
msgid "Small inventory on stock and tires"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_36
#: model:account.group,name:l10n_hr.2_hr_group_36
#: model:account.group.template,name:l10n_hr.hr_group_36
msgid "Small inventory on stock and tires in use"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_363
#: model:account.account,name:l10n_hr.2_hr_363
#: model:account.account.template,name:l10n_hr.hr_363
msgid "Small inventory write off"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_358
#: model:account.account,name:l10n_hr.2_hr_358
#: model:account.account.template,name:l10n_hr.hr_358
msgid "Small inventory's price adjustment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_012
#: model:account.account,name:l10n_hr.2_hr_012
#: model:account.account.template,name:l10n_hr.hr_012
msgid "Software and other rights"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_995
#: model:account.account,name:l10n_hr.2_hr_995
#: model:account.account.template,name:l10n_hr.hr_995
msgid "Source of property plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_996
#: model:account.account,name:l10n_hr.2_hr_996
#: model:account.account.template,name:l10n_hr.hr_996
msgid "Source of rights and obligations"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_328
#: model:account.account,name:l10n_hr.2_hr_328
#: model:account.account.template,name:l10n_hr.hr_328
msgid "Spare part price adjustment"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_32
#: model:account.group,name:l10n_hr.2_hr_group_32
#: model:account.group.template,name:l10n_hr.hr_group_32
msgid "Spare parts"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_405
#: model:account.account,name:l10n_hr.2_hr_405
#: model:account.account.template,name:l10n_hr.hr_405
msgid "Spare parts and material used on maintenance"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_320
#: model:account.account,name:l10n_hr.2_hr_320
#: model:account.account.template,name:l10n_hr.hr_320
msgid "Spare parts on stock"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_42
#: model:account.group,name:l10n_hr.2_hr_group_42
#: model:account.group.template,name:l10n_hr.hr_group_42
msgid "Staff costs-salaries"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_409
#: model:account.account,name:l10n_hr.2_hr_409
#: model:account.account.template,name:l10n_hr.hr_409
msgid "Standard cost adjustments"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_923
#: model:account.account,name:l10n_hr.2_hr_923
#: model:account.account.template,name:l10n_hr.hr_923
msgid "Statutory reserves"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_carried_other_state_O
#: model:account.tax,description:l10n_hr.2_VAT_S_carried_other_state_O
#: model:account.tax.template,description:l10n_hr.VAT_S_carried_other_state_O
msgid "Supplies carried out in another member state"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_new_transport_other_state_O
#: model:account.tax,description:l10n_hr.2_VAT_S_new_transport_other_state_O
#: model:account.tax.template,description:l10n_hr.VAT_S_new_transport_other_state_O
msgid "Supplies of new means of transport to another member state"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_person_not_in_ROC_O
#: model:account.tax,description:l10n_hr.2_VAT_S_person_not_in_ROC_O
#: model:account.tax.template,description:l10n_hr.VAT_S_person_not_in_ROC_O
msgid "Supplies to persons not established in the republic of croatia"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_607
#: model:account.account,name:l10n_hr.2_hr_607
#: model:account.account.template,name:l10n_hr.hr_607
msgid "Suspended production"
msgstr ""

#. module: l10n_hr
#: model:account.report,name:l10n_hr.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_hr
#: model:account.report.column,name:l10n_hr.tax_report_tax_amount
msgid "Tax amount"
msgstr ""

#. module: l10n_hr
#: model:account.report.column,name:l10n_hr.tax_report_tax_base
msgid "Tax base"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_664
#: model:account.account,name:l10n_hr.2_hr_664
#: model:account.account.template,name:l10n_hr.hr_664
msgid "Tax included in value of merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_436
#: model:account.account,name:l10n_hr.2_hr_436
#: model:account.account.template,name:l10n_hr.hr_436
msgid "Tax not-deductible depreciation"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_TAX_PERSON_13%
#: model:account.tax,description:l10n_hr.2_VAT_S_TAX_PERSON_13%
#: model:account.tax.template,description:l10n_hr.VAT_S_TAX_PERSON_13%
msgid "Taxable person not in ROC 13%"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_TAX_PERSON_25%
#: model:account.tax,description:l10n_hr.2_VAT_S_TAX_PERSON_25%
#: model:account.tax.template,description:l10n_hr.VAT_S_TAX_PERSON_25%
msgid "Taxable person not in ROC 25%"
msgstr ""

#. module: l10n_hr
#: model:account.tax,description:l10n_hr.1_VAT_S_TAX_PERSON_5%
#: model:account.tax,description:l10n_hr.2_VAT_S_TAX_PERSON_5%
#: model:account.tax.template,description:l10n_hr.VAT_S_TAX_PERSON_5%
msgid "Taxable person not in ROC 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_467
#: model:account.account,name:l10n_hr.2_hr_467
#: model:account.account.template,name:l10n_hr.hr_467
msgid "Taxes which does not depend on results and charges"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_410
#: model:account.account,name:l10n_hr.2_hr_410
#: model:account.account.template,name:l10n_hr.hr_410
msgid "Telephone and transportation costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_454
#: model:account.account,name:l10n_hr.2_hr_454
#: model:account.account.template,name:l10n_hr.hr_454
msgid "The cost of long term provision for risks within warranty period"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_736
#: model:account.account,name:l10n_hr.2_hr_736
#: model:account.account.template,name:l10n_hr.hr_736
msgid "The costs of goods sold in previous years (returned in this year)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_455
#: model:account.account,name:l10n_hr.2_hr_455
#: model:account.account.template,name:l10n_hr.hr_455
msgid "The costs of long-term provision for Company's restructuring"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_457
#: model:account.account,name:l10n_hr.2_hr_457
#: model:account.account.template,name:l10n_hr.hr_457
msgid "The costs of other provision for other risk and charges"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_456
#: model:account.account,name:l10n_hr.2_hr_456
#: model:account.account.template,name:l10n_hr.hr_456
msgid "The costs of provision for onerous contracts"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_362
#: model:account.account,name:l10n_hr.2_hr_362
#: model:account.account.template,name:l10n_hr.hr_362
msgid "Tires in use"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_352
#: model:account.account,name:l10n_hr.2_hr_352
#: model:account.account.template,name:l10n_hr.hr_352
msgid "Tires on stock"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_365
#: model:account.account,name:l10n_hr.2_hr_365
#: model:account.account.template,name:l10n_hr.hr_365
msgid "Tires write off"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_032
#: model:account.account,name:l10n_hr.2_hr_032
#: model:account.account.template,name:l10n_hr.hr_032
msgid "Tools, transportation equipment and vehicle"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_65
#: model:account.group,name:l10n_hr.2_hr_group_65
#: model:account.group.template,name:l10n_hr.hr_group_65
msgid "Total cost of merchandise goods - total purchasing cost"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_82
#: model:account.group,name:l10n_hr.2_hr_group_82
#: model:account.group.template,name:l10n_hr.hr_group_82
msgid ""
"Total profit or loss for the year (applicable for companies which use msfi "
"and have discontinue operations)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_255
#: model:account.account,name:l10n_hr.2_hr_255
#: model:account.account.template,name:l10n_hr.hr_255
msgid "Trade payable (unpaid long term trade payable)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_223
#: model:account.account,name:l10n_hr.2_hr_223
#: model:account.account.template,name:l10n_hr.hr_223
msgid "Trade payable for utility services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_222
#: model:account.account,name:l10n_hr.2_hr_222
#: model:account.account.template,name:l10n_hr.hr_222
msgid "Trade payable- private person"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_120
#: model:account.account,name:l10n_hr.2_hr_120
#: model:account.account.template,name:l10n_hr.hr_120
msgid "Trade receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1201
#: model:account.account,name:l10n_hr.2_hr_1201
#: model:account.account.template,name:l10n_hr.hr_1201
msgid "Trade receivables (PoS)"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_52
#: model:account.group,name:l10n_hr.2_hr_group_52
#: model:account.group.template,name:l10n_hr.hr_group_52
msgid "Transportation"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_922
#: model:account.account,name:l10n_hr.2_hr_922
#: model:account.account.template,name:l10n_hr.hr_922
msgid "Treasury shares and stakes"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_61
#: model:account.group,name:l10n_hr.2_hr_group_61
#: model:account.group.template,name:l10n_hr.hr_group_61
msgid "Unfinished goods and semi-manufacture goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_611
#: model:account.account,name:l10n_hr.2_hr_611
#: model:account.account.template,name:l10n_hr.hr_611
msgid ""
"Unfinished goods and semi-manufacture goods (analytics by products types)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_735
#: model:account.account,name:l10n_hr.2_hr_735
#: model:account.account.template,name:l10n_hr.hr_735
msgid "Unrealised losses"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_776
#: model:account.account,name:l10n_hr.2_hr_776
#: model:account.account.template,name:l10n_hr.hr_776
msgid "Unrealized gains from financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_297
#: model:account.account,name:l10n_hr.2_hr_297
#: model:account.account.template,name:l10n_hr.hr_297
msgid "Unrealized gains on financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_069
#: model:account.account,name:l10n_hr.2_hr_069
#: model:account.account.template,name:l10n_hr.hr_069
msgid "Unrealized interests on loans etc."
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_478
#: model:account.account,name:l10n_hr.2_hr_478
#: model:account.account.template,name:l10n_hr.hr_478
msgid "Unrealized loss (expense) from financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_4782
#: model:account.account,name:l10n_hr.2_hr_4782
#: model:account.account.template,name:l10n_hr.hr_4782
msgid "Unrealized losses (expenses) from financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_417
#: model:account.account,name:l10n_hr.2_hr_417
#: model:account.account.template,name:l10n_hr.hr_417
msgid "Utilities and similar costs"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_V_0_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_title_prev_calculation
msgid ""
"V. According to the previous calculation: unpaid vat to the day of "
"submitting of this return-overpaid-tax credit"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_685
#: model:account.account,name:l10n_hr.2_hr_685
#: model:account.account.template,name:l10n_hr.hr_685
msgid "VAT included in arts and old timers"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_641
#: model:account.account,name:l10n_hr.2_hr_641
#: model:account.account.template,name:l10n_hr.hr_641
msgid "VAT included in value of goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24004
#: model:account.account,name:l10n_hr.2_hr_24004
#: model:account.account.template,name:l10n_hr.hr_24004
msgid "VAT liability based on own consumption - 25%, 13% or 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240021
#: model:account.account,name:l10n_hr.2_hr_240021
#: model:account.account.template,name:l10n_hr.hr_240021
msgid "VAT liability for advance payment - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240022
#: model:account.account,name:l10n_hr.2_hr_240022
#: model:account.account.template,name:l10n_hr.hr_240022
msgid "VAT liability for advance payment - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240020
#: model:account.account,name:l10n_hr.2_hr_240020
#: model:account.account.template,name:l10n_hr.hr_240020
msgid "VAT liability for advance payment - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24002
#: model:account.account,name:l10n_hr.2_hr_24002
#: model:account.account.template,name:l10n_hr.hr_24002
msgid "VAT liability for advances received"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24042
#: model:account.account,name:l10n_hr.2_hr_24042
#: model:account.account.template,name:l10n_hr.hr_24042
msgid ""
"VAT liability for goods and services received from taxpayers without "
"headquarters in the Republic of Croatia - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24040
#: model:account.account,name:l10n_hr.2_hr_24040
#: model:account.account.template,name:l10n_hr.hr_24040
msgid ""
"VAT liability for goods and services received from taxpayers without "
"headquarters in the Republic of Croatia - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24041
#: model:account.account,name:l10n_hr.2_hr_24041
#: model:account.account.template,name:l10n_hr.hr_24041
msgid ""
"VAT liability for received goods and services from taxpayers without "
"headquarters in the Republic of Croatia - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240031
#: model:account.account,name:l10n_hr.2_hr_240031
#: model:account.account.template,name:l10n_hr.hr_240031
msgid "VAT liability for unbilled deliveries - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240032
#: model:account.account,name:l10n_hr.2_hr_240032
#: model:account.account.template,name:l10n_hr.hr_240032
msgid "VAT liability for unbilled deliveries - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_240030
#: model:account.account,name:l10n_hr.2_hr_240030
#: model:account.account.template,name:l10n_hr.hr_240030
msgid "VAT liability for unbilled deliveries - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2404
#: model:account.account,name:l10n_hr.2_hr_2404
#: model:account.account.template,name:l10n_hr.hr_2404
msgid ""
"VAT liability of taxpayers without headquarters in the Republic of Croatia"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2400
#: model:account.account,name:l10n_hr.2_hr_2400
#: model:account.account.template,name:l10n_hr.hr_2400
msgid "VAT obligations"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2403
#: model:account.account,name:l10n_hr.2_hr_2403
#: model:account.account.template,name:l10n_hr.hr_2403
msgid "VAT obligations for services received from the EU"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24031
#: model:account.account,name:l10n_hr.2_hr_24031
#: model:account.account.template,name:l10n_hr.hr_24031
msgid "VAT obligations for services received from the EU - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24032
#: model:account.account,name:l10n_hr.2_hr_24032
#: model:account.account.template,name:l10n_hr.hr_24032
msgid "VAT obligations for services received from the EU - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24030
#: model:account.account,name:l10n_hr.2_hr_24030
#: model:account.account.template,name:l10n_hr.hr_24030
msgid "VAT obligations for services received from the EU - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_2402
#: model:account.account,name:l10n_hr.2_hr_2402
#: model:account.account.template,name:l10n_hr.hr_2402
msgid "VAT obligations for the acquisition of goods from the EU"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24021
#: model:account.account,name:l10n_hr.2_hr_24021
#: model:account.account.template,name:l10n_hr.hr_24021
msgid "VAT obligations for the acquisition of goods from the EU - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24022
#: model:account.account,name:l10n_hr.2_hr_24022
#: model:account.account.template,name:l10n_hr.hr_24022
msgid "VAT obligations for the acquisition of goods from the EU - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_24020
#: model:account.account,name:l10n_hr.2_hr_24020
#: model:account.account.template,name:l10n_hr.hr_24020
msgid "VAT obligations for the acquisition of goods from the EU - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_1405
#: model:account.account,name:l10n_hr.2_hr_1405
#: model:account.account.template,name:l10n_hr.hr_1405
msgid "VAT on the import of goods"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_VI_0_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_title_total_dif
msgid "VI. Total difference: to pay/to refund"
msgstr ""

#. module: l10n_hr
#: model:account.report.line,name:l10n_hr.tax_report_title_annual_deductible
msgid "VII. Annual deductible proportion(%)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_140
#: model:account.account,name:l10n_hr.2_hr_140
#: model:account.account.template,name:l10n_hr.hr_140
msgid "Value added tax"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_379
#: model:account.account,name:l10n_hr.1_hr_447
#: model:account.account,name:l10n_hr.2_hr_379
#: model:account.account,name:l10n_hr.2_hr_447
#: model:account.account.template,name:l10n_hr.hr_379
#: model:account.account.template,name:l10n_hr.hr_447
msgid "Value adjustment of advance payments"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_679
#: model:account.account,name:l10n_hr.2_hr_679
#: model:account.account.template,name:l10n_hr.hr_679
msgid "Value adjustment of advance payments for merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_139
#: model:account.account,name:l10n_hr.2_hr_139
#: model:account.account.template,name:l10n_hr.hr_139
msgid ""
"Value adjustment of asset receivables from employees and other "
"asset_receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_109
#: model:account.account,name:l10n_hr.2_hr_109
#: model:account.account.template,name:l10n_hr.hr_109
msgid "Value adjustment of bank deposits"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_048
#: model:account.account,name:l10n_hr.1_hr_629
#: model:account.account,name:l10n_hr.2_hr_048
#: model:account.account,name:l10n_hr.2_hr_629
#: model:account.account.template,name:l10n_hr.hr_048
#: model:account.account.template,name:l10n_hr.hr_629
msgid "Value adjustment of biological assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_119
#: model:account.account,name:l10n_hr.2_hr_119
#: model:account.account.template,name:l10n_hr.hr_119
msgid ""
"Value adjustment of current financial assets (value adjustment by analytics "
"of this group of accounts)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_445
#: model:account.account,name:l10n_hr.2_hr_445
#: model:account.account.template,name:l10n_hr.hr_445
msgid "Value adjustment of current receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_444
#: model:account.account,name:l10n_hr.2_hr_444
#: model:account.account.template,name:l10n_hr.hr_444
msgid ""
"Value adjustment of deposit on banks, bills of exchange, cheques (acc.109 "
"and acc. 119)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_639
#: model:account.account,name:l10n_hr.2_hr_639
#: model:account.account.template,name:l10n_hr.hr_639
msgid "Value adjustment of finished goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_649
#: model:account.account,name:l10n_hr.2_hr_649
#: model:account.account.template,name:l10n_hr.hr_649
msgid "Value adjustment of finished goods in own stores"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_018
#: model:account.account,name:l10n_hr.2_hr_018
#: model:account.account.template,name:l10n_hr.hr_018
msgid "Value adjustment of intangible assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_446
#: model:account.account,name:l10n_hr.1_hr_704
#: model:account.account,name:l10n_hr.2_hr_446
#: model:account.account,name:l10n_hr.2_hr_704
#: model:account.account.template,name:l10n_hr.hr_446
#: model:account.account.template,name:l10n_hr.hr_704
msgid "Value adjustment of inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_058
#: model:account.account,name:l10n_hr.2_hr_058
#: model:account.account.template,name:l10n_hr.hr_058
msgid "Value adjustment of investment properties"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_028
#: model:account.account,name:l10n_hr.2_hr_028
#: model:account.account.template,name:l10n_hr.hr_028
msgid "Value adjustment of land and buildings"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_068
#: model:account.account,name:l10n_hr.2_hr_068
#: model:account.account.template,name:l10n_hr.hr_068
msgid "Value adjustment of long-term financial assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_078
#: model:account.account,name:l10n_hr.2_hr_078
#: model:account.account.template,name:l10n_hr.hr_078
msgid "Value adjustment of long-term receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_669
#: model:account.account,name:l10n_hr.2_hr_669
#: model:account.account.template,name:l10n_hr.hr_669
msgid "Value adjustment of merchandise goods"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_44
#: model:account.group,name:l10n_hr.2_hr_group_44
#: model:account.group.template,name:l10n_hr.hr_group_44
msgid "Value adjustment of non-current and current assets"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_440
#: model:account.account,name:l10n_hr.2_hr_440
#: model:account.account.template,name:l10n_hr.hr_440
msgid "Value adjustment of non-current intangible assets (acc. 018)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_442
#: model:account.account,name:l10n_hr.2_hr_442
#: model:account.account.template,name:l10n_hr.hr_442
msgid "Value adjustment of non-current receivables (acc. 078)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_038
#: model:account.account,name:l10n_hr.2_hr_038
#: model:account.account.template,name:l10n_hr.hr_038
msgid "Value adjustment of plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_441
#: model:account.account,name:l10n_hr.2_hr_441
#: model:account.account.template,name:l10n_hr.hr_441
msgid "Value adjustment of property, plant and equipment"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_319
#: model:account.account,name:l10n_hr.2_hr_319
#: model:account.account.template,name:l10n_hr.hr_319
msgid "Value adjustment of raw materials"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_689
#: model:account.account,name:l10n_hr.2_hr_689
#: model:account.account.template,name:l10n_hr.hr_689
msgid ""
"Value adjustment of real estate and works of art, etc. in transactions and "
"advances"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_129
#: model:account.account,name:l10n_hr.2_hr_129
#: model:account.account.template,name:l10n_hr.hr_129
msgid "Value adjustment of receivables"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_448
#: model:account.account,name:l10n_hr.2_hr_448
#: model:account.account.template,name:l10n_hr.hr_448
msgid "Value adjustment of receivables from bankruptcy"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_159
#: model:account.account,name:l10n_hr.2_hr_159
#: model:account.account.template,name:l10n_hr.hr_159
msgid ""
"Value adjustment of receivables from the Government and other institutions"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_359
#: model:account.account,name:l10n_hr.2_hr_359
#: model:account.account.template,name:l10n_hr.hr_359
msgid "Value adjustment of small inventory"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_369
#: model:account.account,name:l10n_hr.2_hr_369
#: model:account.account.template,name:l10n_hr.hr_369
msgid "Value adjustment of small inventory, packaging and tires"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_329
#: model:account.account,name:l10n_hr.2_hr_329
#: model:account.account.template,name:l10n_hr.hr_329
msgid "Value adjustment of spare parts"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_619
#: model:account.account,name:l10n_hr.2_hr_619
#: model:account.account.template,name:l10n_hr.hr_619
msgid "Value adjustment of unfinished goods and semi-manufacture goods"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_609
#: model:account.account,name:l10n_hr.2_hr_609
#: model:account.account.template,name:l10n_hr.hr_609
msgid "Value adjustments of work in progress"
msgstr ""

#. module: l10n_hr
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_0_0_tag_column1
#: model:account.report.expression,report_line_name:l10n_hr.tax_report_line_0_0_tag_column2
#: model:account.report.line,name:l10n_hr.tax_report_title_vat_calculation
msgid "Vat calculation of supplied goods and services - total value(i. + ii.)"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_413
#: model:account.account,name:l10n_hr.2_hr_413
#: model:account.account.template,name:l10n_hr.hr_413
msgid "Vehicle registration and permission costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_501
#: model:account.account,name:l10n_hr.2_hr_501
#: model:account.account.template,name:l10n_hr.hr_501
msgid "Wages and other costs of administration"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_420
#: model:account.account,name:l10n_hr.2_hr_420
#: model:account.account.template,name:l10n_hr.hr_420
msgid "Wages and salaries"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_511
#: model:account.account,name:l10n_hr.2_hr_511
#: model:account.account.template,name:l10n_hr.hr_511
msgid "Wages in selling costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14002
#: model:account.account,name:l10n_hr.2_hr_14002
#: model:account.account.template,name:l10n_hr.hr_14002
msgid "Withholding tax from advances"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14031
#: model:account.account,name:l10n_hr.2_hr_14031
#: model:account.account.template,name:l10n_hr.hr_14031
msgid "Withholding tax from services received from the EU - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14032
#: model:account.account,name:l10n_hr.2_hr_14032
#: model:account.account.template,name:l10n_hr.hr_14032
msgid "Withholding tax from services received from the EU - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14030
#: model:account.account,name:l10n_hr.2_hr_14030
#: model:account.account.template,name:l10n_hr.hr_14030
msgid "Withholding tax from services received from the EU - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14041
#: model:account.account,name:l10n_hr.2_hr_14041
#: model:account.account.template,name:l10n_hr.hr_14041
msgid ""
"Withholding tax from taxpayers without headquarters in the Republic of "
"Croatia - 13%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14042
#: model:account.account,name:l10n_hr.2_hr_14042
#: model:account.account.template,name:l10n_hr.hr_14042
msgid ""
"Withholding tax from taxpayers without headquarters in the Republic of "
"Croatia - 25%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_14040
#: model:account.account,name:l10n_hr.2_hr_14040
#: model:account.account.template,name:l10n_hr.hr_14040
msgid ""
"Withholding tax from taxpayers without headquarters in the Republic of "
"Croatia - 5%"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_600
#: model:account.account,name:l10n_hr.2_hr_600
#: model:account.account.template,name:l10n_hr.hr_600
msgid ""
"Work in progress (allocation by cost driver, cost centres, working orders)"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_60
#: model:account.group,name:l10n_hr.2_hr_group_60
#: model:account.group.template,name:l10n_hr.hr_group_60
msgid "Work in progress - conversion costs"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_601
#: model:account.account,name:l10n_hr.2_hr_601
#: model:account.account.template,name:l10n_hr.hr_601
msgid "Work in progress for services"
msgstr ""

#. module: l10n_hr
#: model:account.account,name:l10n_hr.1_hr_608
#: model:account.account,name:l10n_hr.2_hr_608
#: model:account.account.template,name:l10n_hr.hr_608
msgid "Work in progress under partnership agreement"
msgstr ""

#. module: l10n_hr
#: model:account.group,name:l10n_hr.1_hr_group_6
#: model:account.group,name:l10n_hr.2_hr_group_6
#: model:account.group.template,name:l10n_hr.hr_group_6
msgid ""
"Work in progress, biological assets, finished goods, merchandise goods and "
"non-current assets held for sale"
msgstr ""
