# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mass_mailing_themes
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "$20"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "1958 Royal"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "20%"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "21 Jul"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "<i>DG Connect, UE</i>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "<i>DOE Studio, USA</i>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "<i>Web Designer, ES</i>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "<small>CODE</small>:   <strong class=\"o_code h3\">45A9E77DGW8455</strong>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "<small>Step 1:</small>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "<small>Step 2:</small>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "<small>Step 3:</small>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_bold_template
msgid "<strong>2017</strong>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "<strong>Anna Smith</strong>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "<strong>Clark Hans</strong>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "<strong>John Doe</strong>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_bold_template
msgid "<strong>N°258</strong>"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid ""
"A delicious soup made of one or more seasonal vegetables, without added fat,"
" starches and lactose."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "ALL DAY"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "BBQ Mix"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_bold_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "Banner Image"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Call for Speakers"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Captain’s Salad"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Check the top electric guitar brands and their models!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Choose"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Classic D8 Skate"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid ""
"Cyber-threats continue to increase.<br/>\n"
"                                    The discussion will examine how to develop new norms and integrate them into EU"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "Cybersecurity"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Discover"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid ""
"Discover the new version of our software, with a great overview on all the "
"new features."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "Ecologically sustainable"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Enjoy!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "FROM YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Gibson Les Paul Junior"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "Greatly suits any need"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Heat up the popcorn and get ready for an immersive experience."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Hi there!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "KEYNOTE SPEAKERS"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_bold_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "Logo"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_blank_template
msgid ""
"Lorem ipsum dolor sit amet, <br/><br/>consectetur adipisicing elit, sed do "
"eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim"
" veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea "
"commodo consequat."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_bold_template
msgid ""
"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod no\n"
"                                tempor incididunt ut labore et dolore magna aliqua.\n"
"                                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut\n"
"                                aliquip ex ea commodo consequat."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Mains"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Mini Hamburgers"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "More Info"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "New 45Hc-270A laptop"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid ""
"Normally mirrorless cameras have always sat slightly above entry-level in "
"terms of price.."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "OFF"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "OFF YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Odoo Experience is the place to be to showcase your expertize."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Order"
msgstr "Bílegg"

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "Photo"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "Precision engineered"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Premium Pass"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Puccini Jar"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Read More"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "Read More..."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Recipe of the day"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Register Now!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "Registration"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid "Sony BDVE210"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Starters"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Tagliatelle Boscaiola"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "The Rise Of The Drone"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid ""
"The new laptops look nearly identical to the original models, but closer "
"inspection reveals a refined design."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid ""
"The next edition of Odoo Experience looks very promising. More than 150 talks are scheduled during those 3 days!\n"
"                                    Find below some stand-out talks for the Business tracks."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid ""
"They sit at terminals at Holloman Air Force Base, watching grainy images "
"from a drone video feed."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid "This pass gives you access to the catering and evening activities!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_newsletter_template
msgid ""
"To stir up your curiosity, have a look at all the great talks scheduled and "
"highlighted in our agenda!"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_resto_template
msgid "Tomato Soup"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_tech_template
msgid "Two-lens phone cameras"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "Use This Promo Code BEFORE 1st of August"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "Use now"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_expo_template
msgid "WORKSHOPS"
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_airmail_template
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available.<br/>To welcome"
" you back please accept this 20% discount on you next purchase by clicking "
"the button."
msgstr ""

#. module: mass_mailing_themes
#: model:ir.ui.view,arch_db:mass_mailing_themes.theme_solar_template
msgid "and save $20 on your next order!"
msgstr ""
