<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="repair_line_subcontracting_rule" model="ir.rule">
        <field name="name">Repair Line Subcontractor</field>
        <field name="model_id" ref="repair.model_repair_line"/>
        <field name="domain_force">[
        '|',
            '|',
                ('product_id', 'in', user.partner_id.bom_ids.product_id.ids),
                ('product_id', 'in', user.partner_id.bom_ids.product_tmpl_id.product_variant_ids.ids),
            ('product_id', 'in', user.partner_id.bom_ids.bom_line_ids.product_id.ids),
            ]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

</odoo>
