# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Ukrainian (https://www.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid ""
" If you don't have any account, ask your salesperson to grant you a portal "
"access. "
msgstr ""
"Якщо у вас немає жодного облікового запису, попросіть свого продавця додати "
"доступ до порталу."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "Клієнтський ключ API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login Id"
msgstr "API Login Id"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "Ключ підпису API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "Ключ транзакції API"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: code:addons/payment_authorize/models/authorize_request.py:0
#, python-format
msgid ""
"Authorize.net Error:\n"
"Code: %s\n"
"Message: %s"
msgstr ""
"Помилка Authorize.net:\n"
"Код: %sПовідомлення: %s"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.net Profile ID"
msgstr "Authorize.net Profile ID"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Authorize: received data with missing reference (%s) or trans_id (%s) or "
"fingerprint (%s)"
msgstr ""
"Авторизація: отримані дані з відсутнім посиланням (%s) або trans_id (%s) або"
" відбитки пальців (%s)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "Generate Client Key"
msgstr "Створити клієнтський ключ"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "How to get paid with Authorize.Net"
msgstr "Як отримати оплату за допомогою Authorize.Net"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Invalid token found: the Authorize profile is missing.Please make sure the "
"token has a valid acquirer reference."
msgstr ""
"Знайдено неприйнятний токен: відсутній профіль авторизації. Будь ласка, "
"переконайтеся, що токен має дійсний довідковий номер покупця."

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Платіжний еквайєр"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Токен оплати"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Please complete your profile. "
msgstr "Будь ласка, заповніть ваш профіль."

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Please sign in to complete the payment."
msgstr "Увійдіть, щоби завершити платіж."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__provider
msgid "Provider"
msgstr "Провайдер"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__save_token
msgid "Save Cards"
msgstr "Зберегти картки"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Помилка сервера"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid "The Customer Profile creation in Authorize.NET failed."
msgstr "Не вдалося створити профіль клієнта в Authorize.NET."

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid ""
"The transaction cannot be processed because some contact details are missing"
" or invalid: "
msgstr ""
"Транзакцію неможливо обробити, оскільки деякі контактні дані відсутні або "
"недійсні:"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"This contains the unique reference for this partner/payment token "
"combination in the Authorize.net backend"
msgstr ""
"Містить унікальне посилання для цієї комбінації токенів партнерів/платіжних "
"засобів в автозапуску Authorize.net"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""
"Ця опція дозволяє клієнтам зберігати свою кредитну картку як токен платежу "
"та повторно використовувати її для подальшої покупки. Якщо ви керуєте "
"підписками (повторювані рахунки-фактури), вам потрібно автоматично стягувати"
" плату за клієнта, коли ви видаєте рахунок-фактуру."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Unable to fetch Client Key, make sure the API Login and Transaction Key are "
"correct."
msgstr ""
"Неможливо отримати клієнтський ключ, переконайтесь, що API входу та ключ "
"транзакції є правильними."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid "Warning"
msgstr "Сповіщення"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment."
msgstr "Наразі ми не можемо додати свій спосіб оплати."
