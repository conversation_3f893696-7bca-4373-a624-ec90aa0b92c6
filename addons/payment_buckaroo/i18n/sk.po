# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_buckaroo
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:141
#, python-format
msgid "; multiple order found"
msgstr "; viacnásobná objednávka nájdená"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:139
#, python-format
msgid "; no order found"
msgstr "; žiadna objednávka nájdená"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr "Adyen"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr "Authorize.Net "

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:148
#, python-format
msgid "Buckaroo: invalid shasign, received %s, computed %s, for data %s"
msgstr "Buckaroo: nesprávný shasign, obdržané %s, vyrátané %s pre dáta %s"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:137
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr "Buckaroo: obdržané dáta pre referenciu %s"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:131
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr ""
"Buckaroo: obdržané dáta s chýbajucou referenciou (%s) alebo pay_id (%s) "
"alebo shasign (%s)"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr "Ogone"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Príjemca platby "

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Platobná transakcia"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr "Paypal"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr "Poskytovateľ"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_secretkey
msgid "SecretKey"
msgstr "SecretKey"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr "Sips"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_websitekey
msgid "WebsiteKey"
msgstr "WebsiteKey"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr "Drôtový prenos"
