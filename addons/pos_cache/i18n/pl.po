# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cache
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.warcz<PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <a.wis<PERSON><PERSON>@hadron.eu.com>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: pos_cache
#. odoo-javascript
#: code:addons/pos_cache/static/src/js/Chrome.js:0
#, python-format
msgid "All products are loaded."
msgstr "Wszystkie produkty są załadowane."

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__cache_ids
msgid "Cache"
msgstr "Pamięć podręczna"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__compute_user_id
msgid "Cache compute user"
msgstr "Pamięć podręczna komputera użytkownika"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__config_id
msgid "Config"
msgstr "Konfiguracja"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__id
msgid "ID"
msgstr "ID"

#. module: pos_cache
#: model_terms:ir.ui.view,arch_db:pos_cache.view_pos_config_kanban
msgid "Invalidate cache"
msgstr "Unieważnij pamięć podręczną"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__limit_products_per_request
msgid "Limit Products Per Request"
msgstr "Limit produktów na zapytanie"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__oldest_cache_time
msgid "Oldest cache time"
msgstr "Najstarszy czas cache"

#. module: pos_cache
#: model:ir.actions.server,name:pos_cache.refresh_pos_cache_cron_ir_actions_server
#: model:ir.cron,cron_name:pos_cache.refresh_pos_cache_cron
msgid "PoS: refresh cache"
msgstr "PoS: odśwież cache"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_cache
msgid "Point of Sale Cache"
msgstr "Cache Punktu Sprzedaży"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Konfiguracja punktu sprzedaży"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesja punktu sprzedaży"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_domain
msgid "Product Domain"
msgstr "Domena produktu"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_fields
msgid "Product Fields"
msgstr "Pola produktu"
