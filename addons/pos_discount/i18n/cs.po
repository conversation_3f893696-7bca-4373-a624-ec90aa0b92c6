# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
#, python-format
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point"
" of Sale > Configuration > Settings to set it."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "Umožnit pokladníkovi poskytnout slevy na celou objednávku."

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/xml/DiscountButton.xml:0
#, python-format
msgid "Discount"
msgstr "Sleva"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr "Sleva %"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "Sleva v procentech"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "Zlevněný produkt"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No discount product found"
msgstr "Nenalezen žádný slevový produkt"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No tax"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "Objednat slevy"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavení prodejního místa"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr "Sezení Prodejního místa"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "Tax: %s"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""
"Zdá se, že slevový produkt není správně nakonfigurován. Ujistěte se, že je "
"označeno jako 'Může být prodáno' a 'Dostupné v místě prodeje'."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr ""
