# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * repair
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: repair
#: model:mail.template,report_name:repair.mail_template_repair_quotation
msgid "${(object.name or '').replace('/','_')}"
msgstr ""

#. module: repair
#: model:mail.template,subject:repair.mail_template_repair_quotation
msgid "${object.partner_id.name} Repair Orders (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.partner_id.name} Εντολές Επισκευής (Αναφ. ${object.name or 'μ/δ' })"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "(update)"
msgstr "(ενημέρωση)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'Draft' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Ready to Repair' status is used to start to repairing, user can start repairing only after repair order is confirmed.\n"
"* The 'To be Invoiced' status is used to generate the invoice before or after repairing done.\n"
"* The 'Done' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""

#. module: repair
#: model:mail.template,body_html:repair.mail_template_repair_quotation
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Hello ${object.partner_id.name},<br/>\n"
"        Here is your repair order ${doc_name} <strong>${object.name}</strong>\n"
"        % if object.origin:\n"
"            (with reference: ${object.origin} )\n"
"        % endif\n"
"        % if object.invoice_method != 'none':\n"
"            amounting in <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}.</strong><br/>\n"
"        % else:\n"
"            .<br/>\n"
"        % endif\n"
"        You can reply to this email if you have any questions.\n"
"        <br/><br/>\n"
"        Thank you,\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
msgid ""
"<span class=\"o_form_label\">This operation will cancel the Repair process, "
"but will not cancel it's Invoice. Do you want to continue?</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial Number:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Operations</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Parts</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Printing Date:</strong>"
msgstr "<strong>Ημερομηνία εκτύπωσης:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product to Repair:</strong>"
msgstr "<strong>Είδος για Επισκευή:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Shipping address :</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Σύνολο Χωρίς Φόρους</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total</strong>"
msgstr "<strong>Σύνολο</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Warranty:</strong>"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: repair
#: selection:repair.line,type:0
msgid "Add"
msgstr "Προσθήκη"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes..."
msgstr "Προσθήκη εσωτερικών σημειώσεων..."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add quotation notes..."
msgstr ""

#. module: repair
#: selection:repair.order,invoice_method:0
msgid "After Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: repair
#: selection:repair.order,invoice_method:0
msgid "Before Repair"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Cancel"
msgstr "Ακύρωση"

#. module: repair
#: model:ir.model,name:repair.model_repair_cancel
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr ""

#. module: repair
#: model:ir.actions.act_window,name:repair.action_cancel_repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
msgid "Cancel Repair Order"
msgstr ""

#. module: repair
#: selection:repair.line,state:0 selection:repair.order,state:0
msgid "Cancelled"
msgstr "Ακυρώθηκε"

#. module: repair
#: code:addons/repair/models/repair.py:244
#, python-format
msgid "Cannot cancel completed repairs."
msgstr "Δεν μπορεί να ακυρωθεί μία επιβεβαιωμένη εντολή επισκευής."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Εταιρία"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Επιβεβαίωση Επισκευής"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
#: selection:repair.line,state:0 selection:repair.order,state:0
msgid "Confirmed"
msgstr "Επιβεβαιώθηκε"

#. module: repair
#: code:addons/repair/models/repair.py:607
#: code:addons/repair/models/repair.py:667
#, python-format
msgid ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."
msgstr ""

#. module: repair
#: model:ir.actions.act_window,name:repair.act_repair_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Invoice"
msgstr "Δημιουργία Τιμολογίου"

#. module: repair
#: model:ir.model,name:repair.model_repair_order_make_invoice
msgid "Create Mass Invoice (repair)"
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "Create a new reparation order"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Create invoices"
msgstr "Δημιουργία τιμολογίων"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__create_date
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_date
#: model:ir.model.fields,field_description:repair.field_repair_line__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
msgid "Customer"
msgstr "Πελάτης"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__default_address_id
msgid "Default Address"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__address_id
msgid "Delivery Address"
msgstr "Διεύθυνση Παράδοσης"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__name
#: model:ir.model.fields,field_description:repair.field_repair_line__name
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Description"
msgstr "Περιγραφή"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_dest_id
msgid "Dest. Location"
msgstr "Τοποθεσία Προορ."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__display_name
#: model:ir.model.fields,field_description:repair.field_repair_fee__display_name
#: model:ir.model.fields,field_description:repair.field_repair_line__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Do you really want to create the invoice(s)?"
msgstr "Είστε σίγουροι ότι θέλετε να δημιουργήσετε το τιμολόγιο(α);"

#. module: repair
#: selection:repair.line,state:0
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: repair
#: selection:repair.line,state:0
msgid "Draft"
msgstr "Προσχέδιο"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Extra Info"
msgstr "Περισσότερες Πληροφορίες"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Fees"
msgstr "Προμήθεια"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Μελλοντικές Δραστηριότητες"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__group
msgid "Group by partner invoice address"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "History"
msgstr "Ιστορικό"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__id
#: model:ir.model.fields,field_description:repair.field_repair_fee__id
#: model:ir.model.fields,field_description:repair.field_repair_line__id
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "Κωδικός"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:210
#, python-format
msgid "Insufficient Quantity"
msgstr "Ανεπαρκής Ποσότητα"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Εσωτερικά Σημειώματα"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__move_id
msgid "Inventory Move"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Invoice"
msgstr "Τιμολόγιο"

#. module: repair
#: selection:repair.order,state:0
msgid "Invoice Exception"
msgstr "Εξαίρεση Τιμολογίου"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoice_line_id
#: model:ir.model.fields,field_description:repair.field_repair_line__invoice_line_id
msgid "Invoice Line"
msgstr "Γραμμή Τιμολογίου"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_method
msgid "Invoice Method"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice address:"
msgstr "Διεύθυνση τιμολόγησης:"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice and shipping address:"
msgstr "Invoice and shipping address: "

#. module: repair
#: code:addons/repair/models/repair.py:385
#, python-format
msgid "Invoice created"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_line__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_order__invoiced
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Invoiced"
msgstr "Τιμολογημένα"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_invoice_id
msgid "Invoicing Address"
msgstr "Διεύθυνση Τιμολόγησης"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel____last_update
#: model:ir.model.fields,field_description:repair.field_repair_fee____last_update
#: model:ir.model.fields,field_description:repair.field_repair_line____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice____last_update
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__write_date
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_date
#: model:ir.model.fields,field_description:repair.field_repair_line__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Καθυστερημένες Δραστηριότητες"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Τοποθεσία"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__lot_id
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Παρτίδα/Σειριακός"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Move"
msgstr "Μετακίνηση"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__move_id
msgid "Move created by the repair order"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "My Activities"
msgstr "Οι Δραστηριότητες μου"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: repair
#: selection:repair.order,invoice_method:0
msgid "No Invoice"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:308
#, python-format
msgid "No account defined for partner \"%s\"."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:334
#: code:addons/repair/models/repair.py:362
#, python-format
msgid "No account defined for product \"%s\"."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:598
#: code:addons/repair/models/repair.py:658
#, python-format
msgid "No pricelist found."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:355
#, python-format
msgid "No product defined on fees."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:605
#: code:addons/repair/models/repair.py:665
#, python-format
msgid "No valid pricelist line found."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Notes"
msgstr "Σημειώσεις"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: repair
#: code:addons/repair/models/repair.py:232
#, python-format
msgid "Only draft repairs can be confirmed."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__fees_lines
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Λειτουργίες"

#. module: repair
#: selection:repair.order,activity_state:0
msgid "Overdue"
msgstr "Εκπρόθεσμο"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Partner"
msgstr "Συναλλασόμενος"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__operations
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr ""

#. module: repair
#: selection:repair.order,activity_state:0
msgid "Planned"
msgstr "Προγραμματισμένη"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Price"
msgstr "ΤΙΜΗ"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__pricelist_id
msgid "Pricelist"
msgstr "Τιμοκατάλογος"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__pricelist_id
msgid "Pricelist of the selected partner."
msgstr "Τιμοκατάλογος του επιλεγμένου συνεργάτη."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Print Quotation"
msgstr "Εκτύπωση Προσφοράς"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Είδος"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Ποσότητα Είδους"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Μονάδα Μέτρησης Είδους"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Είδος για Επισκευή"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "Quant"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Quantity"
msgstr "Ποσότητα"

#. module: repair
#: selection:repair.order,state:0
msgid "Quotation"
msgstr "Προσφορά"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
msgid "Quotation / Order"
msgstr "Προσφορά / Εντολή"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__quotation_notes
msgid "Quotation Notes"
msgstr "Σημειώσεις Προσφοράς"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Quotations"
msgstr "Προσφορές"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready To Repair"
msgstr ""

#. module: repair
#: selection:repair.order,state:0
msgid "Ready to Repair"
msgstr ""

#. module: repair
#: selection:repair.line,type:0
msgid "Remove"
msgstr "Αφαίρεση"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
msgid "Repair"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_fee
msgid "Repair Fees"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Line"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_line
msgid "Repair Line (parts)"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_order
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Order"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Order #:"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__repair_id
#: model:ir.model.fields,field_description:repair.field_repair_line__repair_id
msgid "Repair Order Reference"
msgstr ""

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Orders"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Quotation #:"
msgstr "Προσφορά Επισκευής #:"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:196
#, python-format
msgid "Repair must be canceled in order to reset it to draft."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:404
#, python-format
msgid "Repair must be confirmed before starting reparation."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:432
#, python-format
msgid "Repair must be repaired in order to make the product moves."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:415
#, python-format
msgid "Repair must be under repair in order to end reparation."
msgstr ""

#. module: repair
#: code:addons/repair/wizard/repair_cancel.py:20
#, python-format
msgid "Repair order is not invoiced."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repaired
#: selection:repair.order,state:0
msgid "Repaired"
msgstr ""

#. module: repair
#: model:ir.ui.menu,name:repair.menu_repair_order
msgid "Repairs"
msgstr "Επισκευές"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Εντολή Επισκευής"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__invoice_method
msgid ""
"Selecting 'Before Repair' or 'After Repair' will allow you to generate "
"invoice before or after the repair is done respectively. 'No invoice' means "
"you don't want to generate invoice for this repair order."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Send Quotation"
msgstr "Αποστολή Προσφοράς"

#. module: repair
#: code:addons/repair/models/repair.py:545
#, python-format
msgid "Serial number is required for operation line with product '%s'"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Ορισμός σε Πρόχειρη"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Εμφάνιση όλων των εγγραφών όπου η ημερομηνία επόμενης δράσης είναι πριν από "
"σήμερα"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_id
msgid "Source Location"
msgstr "Τοποθεσία Προέλευσης"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__state
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Κατάσταση"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Κίνηση Αποθέματος"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_subtotal
#: model:ir.model.fields,field_description:repair.field_repair_line__price_subtotal
msgid "Subtotal"
msgstr "Μερικό σύνολο"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Tax"
msgstr "Φόρος"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_line__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_tax
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Taxes"
msgstr "Φόροι"

#. module: repair
#: sql_constraint:repair.order:0
msgid "The name of the Repair Order must be unique!"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:172
#, python-format
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:246
#, python-format
msgid "The repair order is already invoiced."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"The repair order uses the warranty date on the Serial Number in\n"
"                order to know if whether the repair should be invoiced to the\n"
"                customer or not."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__state
msgid ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid "This is the location where the product to repair is located."
msgstr ""

#. module: repair
#: selection:repair.order,state:0
msgid "To be Invoiced"
msgstr ""

#. module: repair
#: selection:repair.order,activity_state:0
msgid "Today"
msgstr "Σήμερα"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Σημερινές Δραστηριότητες"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_total
msgid "Total"
msgstr "Σύνολο"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Total amount"
msgstr "Σύνολικό ποσό"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Tracking"
msgstr "Παρακολούθηση"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__type
msgid "Type"
msgstr "Τύπος"

#. module: repair
#: selection:repair.order,state:0
msgid "Under Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_unit
#: model:ir.model.fields,field_description:repair.field_repair_line__price_unit
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Unit Price"
msgstr "Τιμή Μονάδας"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Unit of Measure"
msgstr "Μονάδα Μέτρησης"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Αφορολόγητο Ποσό"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Untaxed amount"
msgstr "Αφορολόγητο ποσό"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:172
#, python-format
msgid "Warning"
msgstr "Προσοχή"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__guarantee_limit
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Warranty Expiration"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
msgid "Yes"
msgstr "Ναι"

#. module: repair
#: code:addons/repair/models/repair.py:600
#: code:addons/repair/models/repair.py:660
#, python-format
msgid ""
"You have to select a pricelist in the Repair form !\n"
" Please set one before choosing a product."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:296
#, python-format
msgid "You have to select an invoice address in the repair form."
msgstr ""
