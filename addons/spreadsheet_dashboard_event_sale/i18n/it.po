# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_event_sale
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Attendees"
msgstr "Partecipanti"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Attuale"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Event by Organizer"
msgstr "Eventi per organizzatore"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Event by Tags"
msgstr "Eventi per etichette"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Event by Template"
msgstr "Eventi per modello"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Event by Venue"
msgstr "Eventi per luogo"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Events"
msgstr "Eventi"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Events Status"
msgstr "Stato eventi"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "KPI"
msgstr "ICP"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Organizer"
msgstr "Organizzatore"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Precedente"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Registration Status"
msgstr "Stato registrazione"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Revenue"
msgstr "Ricavi"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Tag"
msgstr "Etichetta"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Tags"
msgstr "Etichette"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Template"
msgstr "Modello"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Top Organizers"
msgstr "Organizzatori migliori"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Top Tags"
msgstr "Tag migliori"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Top Templates"
msgstr "Modelli migliori"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Top Venues"
msgstr "Luoghi migliori"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "Venue"
msgstr "Luogo"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "attendees - current"
msgstr "partecipanti - attuale"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "attendees - previous"
msgstr "partecipanti - precedente"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "events - current"
msgstr "eventi - attuale"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "events - previous"
msgstr "eventi - precedente"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "dall'ultimo periodo"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "untaxed revenue - current"
msgstr "ricavo imponibile - attuale"

#. module: spreadsheet_dashboard_event_sale
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_event_sale/data/files/events_dashboard.json:0
#, python-format
msgid "untaxed revenue - previous"
msgstr "ricavo imponibile - precedente"
