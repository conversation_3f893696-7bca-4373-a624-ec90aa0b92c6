# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_purchase_stock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-25 10:43+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Last-Translator: Mait<PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid " days"
msgstr "dias"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Amount"
msgstr "Valor"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Amount Purchased"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Average Order"
msgstr "Preço Médio da Encomenda"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Average order"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Buyer"
msgstr "Comprador"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Category"
msgstr "Categoria"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Country"
msgstr "País"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Atual"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Days to confirm"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Days to receive"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Late Receipts"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Lead Time to Receive"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Opened"
msgstr "Aberto"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Order"
msgstr "Ordem"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Ordered"
msgstr "Pedido"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Orders"
msgstr "Ordens"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Período"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produto"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Product Category"
msgstr "Categoria do Artigo"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Partner Country"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Product"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Product Category"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Purchase Representative"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Vendor"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Orders by Untaxed Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchased"
msgstr "Comprado"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Qty Ordered"
msgstr "Qtd Encomendada"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Quantity ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "RFQ"
msgstr "Cotações"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Requests for Quotation by Untaxed Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Responsible"
msgstr "Responsável"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Scheduled on"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Buyers"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Countries"
msgstr "Países Principais"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Orders"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Product Categories"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr "Artigos Principais"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top RFQs"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Vendors"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Total Untaxed"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Transfer"
msgstr "Transferência"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Transfers by Creation Date"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Vendor"
msgstr "Fornecedor"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "last period"
msgstr "último perído"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "purchase stats - current"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "purchase stats - previous"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "desde o período anterior"
