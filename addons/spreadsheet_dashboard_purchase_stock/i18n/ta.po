# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_purchase_stock
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-25 10:43+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Language-Team: Tamil (https://app.transifex.com/odoo/teams/41243/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid " days"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Amount Purchased"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Average Order"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Average order"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Buyer"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Category"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Country"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Current"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Days to confirm"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Days to receive"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Late Receipts"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Lead Time to Receive"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Opened"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Order"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Orders"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Period"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Previous"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Product"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Product Category"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Partner Country"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Product"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Product Category"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Purchase Representative"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Vendor"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchase Orders by Untaxed Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Purchased"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Qty Ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Quantity ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "RFQ"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Requests for Quotation by Untaxed Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Responsible"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Scheduled on"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Buyers"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Countries"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Orders"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Product Categories"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top RFQs"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Top Vendors"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Total Untaxed"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Transfer"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Transfers by Creation Date"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "Vendor"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "purchase stats - current"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "purchase stats - previous"
msgstr ""

#. module: spreadsheet_dashboard_purchase_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase_stock/data/files/purchase_dashboard.json:0
#, python-format
msgid "since last period"
msgstr ""
