# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_sale_timesheet
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billable Hours"
msgstr "Ore fatturabili"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billable Rate"
msgstr "Percentuale fatturabile"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billable hours"
msgstr "Ore fatturabili"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billable rate"
msgstr "Percentuale fatturabile"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billed fixed price"
msgstr "Prezzo fisso fatturato"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billed manually"
msgstr "Fatturati manualmente"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Billed timesheets"
msgstr "Fogli ore fatturati"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Attuale"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Department"
msgstr "Ufficio"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Dipendente"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Grand total"
msgstr "Somma totale"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Hours billed"
msgstr "Ore fatturate"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Hours spent"
msgstr "Ore impiegate"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "KPI"
msgstr "ICP"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Non-billable Hours"
msgstr "Ore non fatturabili"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Non-billable hours"
msgstr "Ore non fatturabili"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Periodo"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Precedente"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Project"
msgstr "Progetto"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Task"
msgstr "Lavoro"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Time Billed by Week"
msgstr "Tempo fatturato per settimana"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Timesheets Analysis by Department"
msgstr "Analisi fogli ore per dipartimento"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Timesheets Analysis by Employee"
msgstr "Analisi fogli ore per dipendente"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Timesheets Analysis by Project"
msgstr "Analisi fogli ore per progetto"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Timesheets Analysis by Task"
msgstr "Analisi fogli ore per lavoro"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Top Departments"
msgstr "Dipartimenti migliori"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr "Dipendenti migliori"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Top Projects"
msgstr "Progetti migliori"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "Top Tasks"
msgstr "Lavori migliori"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "last period"
msgstr "ultimo periodo"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "dall'ultimo periodo"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr "statistiche - attuale"

#. module: spreadsheet_dashboard_sale_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_timesheet/data/files/timesheet_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr "statistiche - precedente"
