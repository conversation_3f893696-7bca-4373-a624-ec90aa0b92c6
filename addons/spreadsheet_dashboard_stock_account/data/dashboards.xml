<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="spreadsheet_dashboard_inventory_on_hand" model="spreadsheet.dashboard">
        <field name="name">Inventory On Hand</field>
        <field name="data" type="base64" file="spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json"/>
        <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_logistics"/>
        <field name="group_ids" eval="[Command.link(ref('stock.group_stock_manager'))]"/>
        <field name="sequence">300</field>
    </record>

</odoo>
