# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock_account
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Language-Team: Norwegian (https://app.transifex.com/odoo/teams/41243/no/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: no\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Current"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Inventory Value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Inventory by Location"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Inventory by Lot/Serial Number"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Inventory by Product"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Location"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Lot/Serial"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "On Hand"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Product"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Reserved"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Stock Quantity by Location"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Top Locations"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Top Lots / Serial Numbers"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "Total inventory value"
msgstr ""

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/inventory_on_hand_dashboard.json:0
#, python-format
msgid "total inventory value"
msgstr ""
