# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# W<PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Manon Rondou, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr " records"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "# Code editor"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d dagen geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d uren geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d minuten geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d maanden geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d jaren geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#, python-format
msgid "%s Files"
msgstr "%s Bestanden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "%s days ago"
msgstr "%s dagen geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "%s records"
msgstr "%s records"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' is geen correcte datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr "'%s' is geen correcte datum of datum/tijd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' is is geen correcte datum, datum/tijd en/of tijd."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' is geen geldige datum/tijd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' is geen correcte float"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' is geen correct geheel getal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "'%s' is geen correct  valuta veld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr ""
"'%s' is niet converteerbaar naar een datum, een datum/tijd of een tijd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr "'%s' is niet gesynchroniseerd met '%s'."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "(%s/%sMB)"
msgstr "(%s/%sMB)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "(Community Edition)"
msgstr "(Community-editie)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr "(wijzigen)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(aantal)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(create)"
msgstr "(aanmaken)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#, python-format
msgid "(current)"
msgstr "(huidig)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr "(Geen resultaat)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "(no string)"
msgstr "(geen string)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(geen label)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid ""
") format(\"woff\");\n"
"                        font-weight: normal;\n"
"                        font-style: normal;\n"
"                    }"
msgstr ""
") format(\"woff\");\n"
"                        font-weight: normal;\n"
"                        font-style: normal;\n"
"                    }"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "+ KEY"
msgstr "+ KEY"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr "07/08/2020"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr "08/07/2020"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "1 record"
msgstr "1 record"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr "<span class=\"text-nowrap\">$ 2.887,50</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137,50</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$<span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr "<span class=\"text-nowrap\">1,500.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr "<span class=\"text-nowrap\">2,350.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr "<span class=\"text-nowrap\">BTW 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                       Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                       Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">Tax 15%</span>"
msgstr "<span id=\"line_tax_ids\">BTW 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr "<span itemprop=\"name\">Deco Addict</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr "<span>$<span class=\"oe_currency_value\">19,250.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.00</span>"
msgstr "<span>5.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr "<span>Bedrag</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>Omschrijving</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2023/00003</span>"
msgstr ""
"<span>Factuur</span>\n"
"                           <span>INV/2023/00003</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr "<span>Betaaltermijn: 30 Days</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>Hoeveelheid</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>BTW </span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>Eenheidsprijs</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""
"<span>[FURN_8220] Vier persoons bureau<br/>\n"
"                                       Vierpersoons moderne kantoorwerkplek</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""
"<span>[FURN_8999] Driezitsbank<br/>\n"
"                                       Driezitsbank met ligstoel in staalgrijze kleur</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Vervaldatum:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Factuurdatum:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>Totaal</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Untaxed Amount</strong>"
msgstr "<strong>Onbelast bedrag</strong>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "=ilike"
msgstr "=ilike"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "=like"
msgstr "=like"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid ""
"@font-face {\n"
"                        font-family: \"font\";\n"
"                        src: url(data:font/ttf;base64,"
msgstr ""
"@font-face {\n"
"                        font-family: \"font\";\n"
"                        src: url(data:font/ttf;base64,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr "Er bestaat al een filter met dezelfde naam."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr "Een naam voor je favoriete filter is vereist."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"Een pop-up bericht met je rapport is geblokkeerd. Je moet wellicht je "
"browser instellingen veranderen zodat pop-ups van deze pagina worden "
"toegestaan."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "ALLE"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "ÉÉN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "Toegang geweigerd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr "Toegangsfout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Toegang tot alle Enterprise apps"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/ace/ace_field.js:0
#, python-format
msgid "Ace Editor"
msgstr "Ace Editor"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/views/form/status_bar_buttons/status_bar_buttons.xml:0
#, python-format
msgid "Action"
msgstr "Actie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Action ID:"
msgstr "Actie ID:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Actie venster weergave"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "Assets debugging activeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr "Test assets debugging activeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode (with assets)"
msgstr "Debugmodus activeren (met assets)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode"
msgstr "Activeer de ontwikkelaarsmodus"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with assets)"
msgstr "Activeer de ontwikkelaarsmodus (met assets)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with tests assets)"
msgstr "Activeer de ontwikkelaarsmodus (met test assets)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
#, python-format
msgid "Add"
msgstr "Toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "Een aangepaste filter toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "Aangepaste groep toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
#, python-format
msgid "Add a Property"
msgstr "Een eigenschap toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Add a Value"
msgstr "Voeg een waarde toe"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "Toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Add a line"
msgstr "Regel toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "Branch toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "Add column"
msgstr "Kolom toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "Voeg filter toe"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "Nieuwe waarde toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "Node toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr "Context van qweb-richtlijn toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "Label toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "Toevoegen aan favorieten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "Toevoegen: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#, python-format
msgid "Add: %s"
msgstr "Toevoegen: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#, python-format
msgid "Additionnal actions"
msgstr "Extra acties"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt & Sign"
msgstr "Bevestigen en ondertekenen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "Jouw eigen handtekening"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "Waarschuwing"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "All"
msgstr "Alle"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#, python-format
msgid "All day"
msgstr "Hele dag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "Alle gebruikers"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Among the"
msgstr "Onder de"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#, python-format
msgid "An error occured while uploading."
msgstr "Er is een fout opgetreden tijdens het uploaden."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr "Er is een fout opgetreden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "And more"
msgstr "En meer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "Één"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr ""
"Verschijnt standaard in de rechterbovenhoek van je documenten (rapport kop)."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#: code:addons/web/static/src/views/fields/daterange/daterange_field.js:0
#, python-format
msgid "Apply"
msgstr "Toepassen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Archiveer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Archive All"
msgstr "Archiveer alles"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "Ben je zeker dat je alle records wilt archiveren uit deze kolom?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "Ben je zeker dat je alle geselecteerde records wilt archiveren?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "Ben je zeker dat je dit record wilt archiveren?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Weet je zeker dat je deze kolom wil verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Ben je zeker dat je dit filter wilt verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr "Ben je zeker dat je deze records wilt verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "Ben je zeker dat je deze records wilt verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Are you sure you want to delete this column?"
msgstr "Weet je zeker dat je deze kolom wilt verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid ""
"Are you sure you want to delete this property field? It will be removed for "
"everyone using the \"%s\" %s."
msgstr ""
"Weet je zeker dat je dit eigenschapsveld wilt verwijderen? Het wordt "
"verwijderd voor iedereen die de \"%s\" %s gebruikt."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Weet je zeker dat je dit record wilt verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Ben je zeker dat je dit record wilt verwijderen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "Ben je zeker dat je de volgende update wilt uitvoeren op deze"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "As a default text when no value are set"
msgstr "Als standaardtekst wanneer er geen waarde is ingesteld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Ascending"
msgstr "Oplopend"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Attach"
msgstr "Bijvoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Attachment"
msgstr "Bijlage"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.js:0
#, python-format
msgid "Attachment Image"
msgstr "Bijlage afbeelding"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "Auto"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Available fields"
msgstr "Beschikbare velden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr "Achtergrondafbeelding"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#: code:addons/web/static/src/views/fields/badge/badge_field.js:0
#, python-format
msgid "Badge"
msgstr "Badge"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/badge_selection/badge_selection_field.js:0
#, python-format
msgid "Badges"
msgstr "Badges"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Bar Chart"
msgstr "Staafdiagram"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Basis"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr "Superuser worden"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""
"Binaire velden kunnen niet naar Excel worden geëxporteerd, maar niet als de "
"inhoud base64-gecodeerd is. Dat lijkt niet het geval te zijn voor %s."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
#, python-format
msgid "Binary file"
msgstr "Binair bestand"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "Gegarandeerde bugfixees"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Button"
msgstr "Knop"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Button Type:"
msgstr "Soort knop:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Door op Aannemen & Ondertekenen te klikken, ga ik ermee akkoord dat de "
"gekozen handtekening/initialen een geldige elektronische weergave zijn van "
"mijn handgeschreven handtekening/initialen voor alle doeleinden wanneer deze"
" wordt gebruikt op documenten, inclusief juridisch bindende contracten."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "Bytes"
msgstr "Bytes"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "CLEAR"
msgstr "WISSEN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "CMD"
msgstr "CMD"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "CTRL"
msgstr "CTRL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/phone/phone_field.xml:0
#, python-format
msgid "Call"
msgstr "Bel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/daterange/daterange_field.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "Annuleren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr "Annuleer upload"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "Kaartkleur: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Change default:"
msgstr "Wijzig standaard:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Change graph"
msgstr "Wijzig grafiek"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#: code:addons/web/static/src/views/fields/boolean/boolean_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Checkbox"
msgstr "Selectievakje"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_checkboxes/many2many_checkboxes_field.js:0
#, python-format
msgid "Checkboxes"
msgstr "Selectievakjes"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr "Kies"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr "Kies bestand"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr "Kies een debug-opdracht..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Clear"
msgstr "Wissen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "Handtekening wissen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Close"
msgstr "Sluiten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#, python-format
msgid "Close menu"
msgstr "Menu sluiten"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr "Kleuren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "Kolom %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#: model:ir.model,name:web.model_res_company
#, python-format
msgid "Companies"
msgstr "Bedrijven"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr "Bedrijfsgegevens"

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr "Bedrijf document lay-out"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr "Bedrijfslogo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "Bedrijfsnaam"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr "Bedrijfsslogan"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "Bedrijfsnaam"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr "Vergelijking"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "Voorwaarde:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr "Configureer je document lay-out"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Bevestiging"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr "Verbinding verbroken. Probeer opnieuw verbinding te maken ..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr "Verbinding hersteld. Je bent weer online."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Context:"
msgstr "Context:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "Controlepaneel knoppen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copied"
msgstr "Gekopieerd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr "Gekopieerd!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy"
msgstr "Kopieer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy Multiline Text to Clipboard"
msgstr "Kopieer meerregelige tekst naar klembord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy Text to Clipboard"
msgstr "Tekst kopiëren naar klembord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy URL to Clipboard"
msgstr "URL kopiëren naar klembord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "Kopieer de volledige foutmelding naar het klembord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "Kopieer naar het klembord."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr "Copyright &amp;copy;"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "Copyright © 2004"
msgstr "Auteursrecht © 2004"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Correct issues to save, or discard changes"
msgstr "Los de problemen op om op te slaan of verwijder alle wijzigingen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "Kon niet verbinden met de server"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
#, python-format
msgid "Could not display the selected image"
msgstr "Kon de geselecteerde afbeelding niet weergeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
#, python-format
msgid "Could not display the selected pdf"
msgstr "Kon de geselecteerde pdf niet weergeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr "Kon de opgegeven afbeeldings-url niet weergeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "Kon XML niet serialiseren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""
"Kon de omslagafbeelding niet instellen: onjuist veld (\"%s\") wordt "
"weergegeven in de weergave."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "Could not start scanning. "
msgstr "Kan het scannen niet starten. "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/utils.js:0
#, python-format
msgid "Count"
msgstr "Aantal"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "Land"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Create"
msgstr "Aanmaken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "Aanmaken "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create \"%s\""
msgstr "Maak \"%s\" aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Aanmaken \"<strong>%s</strong>\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create %s"
msgstr "%s . maken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "Create <strong>%s</strong> as a new %s?"
msgstr "<strong>%s</strong> aanmaken als een nieuw %s?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "Aanmaken en bewerken..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create and edit..."
msgstr "Maken en bewerken..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
#, python-format
msgid "Create record"
msgstr "Record maken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr "Aanmaken: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "Aanmaakdatum:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "Aangemaakt door:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Cumulative"
msgstr "Cumulatief"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "Huidige status"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr "Aangepaste kleuren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr "Donkerblauw"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr "Donkerpaars"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Database"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/date/date_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Date"
msgstr "Datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Date & Time"
msgstr "Datum & Tijd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Day"
msgstr "Dag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr "Debugmodus deactiveren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Deactivate the developer mode"
msgstr "Deactiveer de ontwikkelaarsmodus"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr "Hulpprogramma's voor foutopsporing..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Decimal"
msgstr "Decimaal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "Standaard"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Default State"
msgstr "Standaardstatus"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Default Value"
msgstr "Standaardwaarde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Default text is used when no values are set"
msgstr "Standaardtekst wordt gebruikt als er geen waarden zijn ingesteld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Default value"
msgstr "Standaard waarde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Default:"
msgstr "Standaard:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_tags/tags_list.xml:0
#: code:addons/web/static/src/views/fields/many2many_tags/tags_list.xml:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Delete"
msgstr "Verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Delete Property Field"
msgstr "Eigenschappenveld verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr "Verwijder item"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr "Node verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Delete row"
msgstr "Verwijder rij"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "Verwijder rij"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Descending"
msgstr "Aflopend"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Developer Tools"
msgstr "Ontwikkelaarstools"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
#, python-format
msgid "Different currencies cannot be aggregated"
msgstr "Verschillende valuta's kunnen niet worden samengevoegd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#, python-format
msgid "Discard"
msgstr "Negeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Discard changes"
msgstr "Wijzigingen negeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr "Record negeren"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr "Wil je de upload van %s echt annuleren?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "Wil je echt dit exportsjabloon verwijderen?"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr "Documentsjabloon"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr "Documentatie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Domain"
msgstr "Domein"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr "Domeinknooppunt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr "Domein niet correct opgegeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr "Domein niet ondersteund"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Domain:"
msgstr "Domein:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr "Ga nog niet weg,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Nog niet weggaan,<br />het systeem is nog steeds aan het laden..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Download"
msgstr "Downloaden"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr "PDF-voorbeeld downloaden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Download xlsx"
msgstr "Download xlsx"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "Teken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Dropdown menu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "Dupliceren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
#, python-format
msgid "Edit"
msgstr "Bewerken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "Bewerken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "Kolom bewerken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Edit Domain"
msgstr "Bewerk domein"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit SearchView"
msgstr "Zoekweergave bewerken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Edit Stage"
msgstr "Bewerk fase"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr "Weergave bewerken: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr "Bewerk record"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Edit: %s"
msgstr "Bewerken: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/email/email_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Empty email address"
msgstr "Leeg e-mailadres"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr "Profilering inschakelen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Enter e-mail address"
msgstr "Vul email adres in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/highlight_text/form_label_highlight_text.xml:0
#, python-format
msgid "Enterprise"
msgstr "Onderneming"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Entry Count"
msgstr "Invoeraantal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "Error"
msgstr "Fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "Esc to discard"
msgstr "Esc om af te negeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "Agenda van iedereen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "Alles"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Expand all"
msgstr "Alles uitvouwen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Export"
msgstr "Exporteren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Export All"
msgstr "Exporteer alles"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Export Data"
msgstr "Gegevens exporteren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Export Format:"
msgstr "Export formaat:"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr "Gegroepeerde data exporteren naar CSV is niet ondersteund."

#. module: web
#. odoo-javascript
#. odoo-python
#: code:addons/web/controllers/export.py:0
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "External ID"
msgstr "Externe ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "Externe link"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#, python-format
msgid "FILTER"
msgstr "FILTER"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr "Evalueren van zoekcontext mislukt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "Onwaar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_favorite/boolean_favorite_field.js:0
#, python-format
msgid "Favorite"
msgstr "Favoriet"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "Favorieten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Field Type"
msgstr "Soort veld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Field:"
msgstr "Veld:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Fields to export"
msgstr "Te exporteren velden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/binary/binary_field.js:0
#, python-format
msgid "File"
msgstr "Bestand"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "Bestand uploaden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "Filter met dezelfde naam bestaat al."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "Filters"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Flip axis"
msgstr "As wisselen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
#, python-format
msgid "Float"
msgstr "Getal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Fold"
msgstr "Vouwen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "Gevolgd door"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#, python-format
msgid "Followed-by"
msgstr "Gevolgd door"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr "Lettertype"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/font_selection/font_selection_field.js:0
#, python-format
msgid "Font Selection"
msgstr "Lettertype selecteren"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr "Voettekst"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "Voettekst, weergegeven aan de onderzijde van alle rapportages."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"Voor compatibiliteit met Excel, data kan niet geëxporteerd worden als er meer dan 16384 kolommen zijn\n"
"\n"
"Tip: probeer de assen te wisselen, filter of beperk het aantal kolommen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "Formulier"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr "Vrijheid weergave"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Fri"
msgstr "Vrij"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#, python-format
msgid "Fuchsia"
msgstr "Fuchsia"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "Volledige naam"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr "Fuchsia"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "GNU LGPL Licensed"
msgstr "GNU LGPL-licentie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Get View"
msgstr "Weergave bekijken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "Krijg deze optie en veel meer met Odoo Enterprise!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/url/url_field.xml:0
#: code:addons/web/static/src/views/fields/url/url_field.xml:0
#, python-format
msgid "Go to URL"
msgstr "Ga naar de URL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "Grafiek"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "Groen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#, python-format
msgid "Group By"
msgstr "Groeperen op"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/handle/handle_field.js:0
#, python-format
msgid "Handle"
msgstr "Handvat"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "Koptekst weergegeven boven aan alle rapportages."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#, python-format
msgid ""
"Heads up! Your recent changes are too large to save automatically. Please "
"click the <i class=\"fa fa-cloud-upload fa-fw\"></i> button now to ensure "
"your work is saved before you exit this tab."
msgstr ""
"Let op! Je recente wijzigingen zijn te groot om automatisch op te slaan. "
"Klik nu op de knop <i class=\"fa fa-cloud-upload fa-fw\"></i> om ervoor te "
"zorgen dat je werk wordt opgeslagen voordat je dit tabblad verlaat."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "Verbergen in Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.xml:0
#, python-format
msgid "Hide in kanban"
msgstr "Verbergen in kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr "Druk op OMLAAG om naar de onderstaande lijst te navigeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "Druk op ENTER om"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "Druk op ENTER om AAN TE MAKEN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "Druk op ENTER om OP TE SLAAN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "Druk op ESCAPE om TE NEGEREN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "Ik weet dit zeker"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "Ik wil gegevens bijwerken (import-compatibele export)"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr ""
"Als je %s of %s wijzigt, wordt de synchronisatie opnieuw toegepast en worden"
" de gegevens gewijzigd."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.xml:0
#, python-format
msgid "Image"
msgstr "Afbeelding"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "In %s days"
msgstr "Over %s dagen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/integer/integer_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Integer"
msgstr "Geheel getal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#, python-format
msgid "Internal link"
msgstr "Interne link"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr "Interval"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Invalid data"
msgstr "Ongeldige gegevens"

#. module: web
#. odoo-python
#: code:addons/web/controllers/database.py:0
#: code:addons/web/controllers/database.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""
"Ongeldige database naam. Allee alfanumerieke karakters, onderstreep, "
"koppelteken en punten zijn niet toegestaan."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Invalid domain"
msgstr "Foutief domein"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr "Incorrecte veld volgorde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr ""
"Ongeldige veld combinatie. Je hebt misschien een niet bestaande veldnaam of "
"een niet-relationeel veld gevolgd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "Invalid fields"
msgstr "Ongeldige veldeb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr "Ongeldige velden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "Invalid fields: "
msgstr "Ongeldige velden: "

#. module: web
#. odoo-python
#: code:addons/web/controllers/domain.py:0
#, python-format
msgid "Invalid model: %s"
msgstr "Ongeldig model: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Invite"
msgstr "Uitnodigen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Invite New Users"
msgstr "Nieuwe gebruikers uitnodigen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Inviting..."
msgstr "Uitnodigend..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__is_company_details_empty
msgid "Is Company Details Empty"
msgstr "Als de bedrijfsgegevens leeg zijn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""
"Het is mogelijk dat de \"t-call\"-tijd niet overeenkomt met de totale tijd van de\n"
"            sjabloon. Omdat de globale tijd (in de vervolgkeuzelijst) geen rekening houdt met de\n"
"            duur die niet in de weergave staat (zoek naar de sjabloon, lees, overerving,\n"
"            compilatie...). Tijdens het renderen neemt de globale tijd ook een deel van de tijd in beslag om te maken\n"
"            het profiel en een deel dat niet is aangemeld in de functie die door de qweb is gegenereerd."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.js:0
#, python-format
msgid "Kanban Examples"
msgstr "Kanban-voorbeelden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr "Kanban: geen actie voor type:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/label_selection/label_selection_field.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
#, python-format
msgid "Label Selection"
msgstr "Labelselectie"

#. module: web
#. odoo-python
#: code:addons/web/controllers/session.py:0
#, python-format
msgid "Languages"
msgstr "Talen"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "Laatste aanpassingsdatum:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "Laatst aangepast door:"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr "Layout"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr "Achtergrond lay-out"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "Ontwikkelaarstools verlaten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr "Lichtblauw"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Line Chart"
msgstr "Lijndiagram"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "Lijst"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "Laden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Load demo data"
msgstr "Laad demo data"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Load everything anyway."
msgstr "Verdergaan met laden."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Load more... ("
msgstr "Laad meer... ("

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "Laden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "Laden, even geduld a.je.b."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Loading..."
msgstr "Laden..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Login"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "Log in als superuser"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: model_terms:ir.ui.view,arch_db:web.login_successful
#, python-format
msgid "Log out"
msgstr "Afmelden"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr "Primaire kleur logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr "Secundaire kleur logo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr "MailDeliveryException"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Main actions"
msgstr "Belangrijkste acties"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Manage Attachments"
msgstr "Bijlagen beheren"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Beheer databases"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "Filters beheren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one_barcode/many2one_barcode_field.js:0
#, python-format
msgid "Many2OneBarcode"
msgstr "Many2OneBarcode"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Many2many"
msgstr "Many2many"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Many2one"
msgstr "Many2one"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "Match"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "Match records die voldoen aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "Match records met de volgende regel:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""
"Misschien kan je overwegen de applicatie te herladen door F5 te drukken..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr "Meetwaarden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr "Middelblauw"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr "Afspraak onderwerp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr "Onderwerp afspraak:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: model:ir.model,name:web.model_ir_ui_menu
#, python-format
msgid "Menu"
msgstr "Menu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Method:"
msgstr "Methode:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr "Ontbrekend record"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Mobile support"
msgstr "Mobiele ondersteuning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Model"
msgstr "Type"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr "Model recordregels"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Model:"
msgstr "Model:"

#. module: web
#: model:ir.model,name:web.model_ir_model
msgid "Models"
msgstr "Modellen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Modifiers:"
msgstr "Modificatoren:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Mon"
msgstr "Ma"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/monetary/monetary_field.js:0
#, python-format
msgid "Monetary"
msgstr "Monetair"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Month"
msgstr "Maand"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#: code:addons/web/static/src/views/form/button_box/button_box.xml:0
#, python-format
msgid "More"
msgstr "Meer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
#, python-format
msgid "Move to %s..."
msgstr "Ga naar %s..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/text/text_field.js:0
#, python-format
msgid "Multiline Text"
msgstr "Meerdere regels tekst"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr "Mijn Odoo.com account"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr "GEEN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "New"
msgstr "Nieuw"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr "Nieuw %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "New Event"
msgstr "Nieuw evenement"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
#, python-format
msgid "New Property"
msgstr "Nieuw pand"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "New design"
msgstr "Nieuw ontwerp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "New template"
msgstr "Nieuw sjabloon"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "New:"
msgstr "Nieuw:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "New: %s"
msgstr "Nieuw: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#, python-format
msgid "Next"
msgstr "Volgende"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr "Volgende pagina"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "No"
msgstr "Nee"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_value.js:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#, python-format
msgid "No Access"
msgstr "Geen toegang"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr "Geen update:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr "Geen actie met id '%s' gevonden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr "Geen kleur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "No command found"
msgstr "Geen commando gevonden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "Geen data"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr "Geen gegevens om te tonen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No debug command found"
msgstr "Geen debug-opdracht gevonden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "No device can be found."
msgstr "Er kan geen apparaat worden gevonden."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "No match found."
msgstr "Geen overeenkomsten gevonden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr "Geen menu gevonden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "No records"
msgstr "Geen regels"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "Geen records gevonden!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "No result"
msgstr "Geen resultaat"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No result found"
msgstr "Geen resultaat gevonden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "No valid record to save"
msgstr "Geen geldig record om te bewaren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr "Er is geen weergave van het type '%s' gevonden in de huidige actie."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "None"
msgstr "Geen"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "Niet ingesteld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr "Niet actieve status"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "Niet actieve status, klik om te wijzigen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Object:"
msgstr "Object:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo apps zijn binnenkort beschikbaar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "Odoo cliënt fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr "Odoo fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr "Odoo-netwerkfout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "Odoo S.A."
msgstr "Odoo SA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr "Odoo-serverfout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "Odoo sessie verlopen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo foutmelding"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "Odoo needs your authorization first."
msgstr "Odoo heeft eerst je autorisatie nodig."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr ""
"Van de %d geselecteerde records zijn enkel de eerste %d "
"gearchiveerd/gedearchiveerd."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#, python-format
msgid "Oh snap!"
msgstr "Oh nee!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "Bij wijziging:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr "One2many"

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr ""
"Alleen werknemers hebben toegang tot deze database. Neem dan contact op met "
"de beheerder."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr "Enkel de eerste %d records zijn verwijderd (van de %d geselecteerde)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Only the first %s records have been deleted (out of %s selected)"
msgstr "Alleen de eerste %s records zijn verwijderd (van %s geselecteerd)"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""
"Alleen de types %(supported_types)s zijn ondersteund voor deze categorie "
"(type %(field_type)s gevonden)"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""
"Alleen de types%(supported_types)s zijn ondersteund voor filters (gevonden "
"type %(field_type)s)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "Alleen jij"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr "Weergave openen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.xml:0
#, python-format
msgid "Open developer tools"
msgstr "Ontwikkelaarstools openen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Open:"
msgstr "Open:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "Open: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Open: %s"
msgstr "Openen: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Option Name"
msgstr "Optienaam"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr "Optionele kolommen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr "Oranje"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#: code:addons/web/static/src/views/kanban/kanban_model.js:0
#, python-format
msgid "Other"
msgstr "Overige"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
#, python-format
msgid "PDF Viewer"
msgstr "PDF Viewer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr "PDF-besturingen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF bestand"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"Pagina:\n"
"                    <span class=\"page\"/>\n"
"                    van\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "Pagina: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr "Pager"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr "Papierformaat"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "Relatie"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Wachtwoord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Pending Invitations:"
msgstr "In afwachting van uitnodigingen:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/percent_pie/percent_pie_field.js:0
#, python-format
msgid "PercentPie"
msgstr "ProcentPie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/percentage/percentage_field.js:0
#, python-format
msgid "Percentage"
msgstr "Percentage"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "Percentage taart"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/phone/phone_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "Telefoon"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr "Kies een kleur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Pie Chart"
msgstr "Cirkeldiagram"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"De cirkeldiagram kan geen positieve en negatieve waarden combineren. Probeer"
" je domein te wijzigen om positieve resultaten te krijgen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "Draaitabel"

#. module: web
#. odoo-python
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr "Draaitabel %(title)s (%(model_name)s)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Pivot settings"
msgstr "Draaitabel instellingen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr "Wees alsjeblieft geduldig."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "Klik eerst op de \"Opslaan\" knop"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Please complete your properties before adding a new one"
msgstr "Voltooi je eigendommen voordat je een nieuwe toevoegt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr "Voer een numerieke waarde in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "Geef aub 'opslaan veldenlijst' naam in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr "Sla op voordat je een bestand toevoegt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "Selecteer velden om te exporteren..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "Selecteer velden voor opslaan export lijst..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "Je dient de vertalingen bij te werken van:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""
"Je dient de kopieer knop te gebruiken om de fout te melden aan de "
"ondersteuning."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2023/00003</span></b>"
msgstr ""
"Gebruik a.u.b. de volgende communicatie voor uw betaling: <b><span>\n"
"                           INV/2023/00003</span></b>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "Please, scan again !"
msgstr "Alsjeblieft, scan opnieuw!"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "Aangeboden door %s%s"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Aangeboden door <span>Odoo</span>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "Voorkeuren"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr "Voorbeeld"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "Voorbeeldweergave externe rapportage"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "Voorbeeldweergave interne rapportage"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr "Voorbeeld logo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#, python-format
msgid "Previous"
msgstr "Vorige"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr "Vorige periode"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr "Vorig jaar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr "Vorig menu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr "Vorige pagina"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr "Primaire kleur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
#, python-format
msgid "Print"
msgstr "Afdrukken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#, python-format
msgid "Printing options"
msgstr "Afdrukopties"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.xml:0
#, python-format
msgid "Priority"
msgstr "Prioriteit"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "Processing..."
msgstr "Verwerken..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
#, python-format
msgid "Progress Bar"
msgstr "Voortgangsbalk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Properties"
msgstr "Eigenschappen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Property %s"
msgstr "Eigenschap %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Property Name"
msgstr "Eigendomsnaam"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr "Paars"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr "Q1"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr "Q2"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr "Q3"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr "Q4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr "Kwartaal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Quick add"
msgstr "Snel toevoegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Quick search: %s"
msgstr "Snelzoeken: %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr "Qweb veld afbeelding"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr "RGB"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr "RGBA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/radio/radio_field.js:0
#, python-format
msgid "Radio"
msgstr "Keuzerondje"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr "Qweb opnemen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr "SQL opnemen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr "Traces opnemen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Recording..."
msgstr "Opnemen..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr "Rood"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/reference/reference_field.js:0
#, python-format
msgid "Reference"
msgstr "Referentie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Refresh"
msgstr "Vernieuw"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr "Assets bundels opnieuw genereren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Relation not allowed"
msgstr "Relatie niet toegestaan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "Relatie om te volgen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Relation:"
msgstr "Relatie:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#, python-format
msgid "Relational table"
msgstr "Tabelrelatie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Remaining Days"
msgstr "Resterende dagen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#, python-format
msgid "Remove"
msgstr "Verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Remove Cover Image"
msgstr "Verwijder omslagafbeelding"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Remove Property"
msgstr "Eigenschap verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Remove field"
msgstr "Verwijder veld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "Verwijderen uit favorieten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "Label verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "Verwijder deze favoriet van de lijst"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "Rapport"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "Rapport voettekst"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr "Rapport lay-out"

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr "Voorbeeld van rapportlay-out"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr "Verzoek time-out"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#, python-format
msgid "Reset domain"
msgstr "Domein resetten"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr "Reset naar logo kleuren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "Klik overal test uitvoeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "JS mobiele testen uitvoeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr "JS testen uitvoeren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#, python-format
msgid "SEE RESULT"
msgstr "BEKIJK RESULTAAT"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
#, python-format
msgid "SIGNATURE"
msgstr "HANDTEKENING"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr "Zalmroze"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Sat"
msgstr "Za"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "Opslaan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#, python-format
msgid "Save & Close"
msgstr "Opslaan & Sluiten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#, python-format
msgid "Save & New"
msgstr "Opslaan & Nieuw"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "Opslaan als:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Save as:"
msgstr "Opslaan als:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr "Huidige zoekopdracht opslaan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "Standaard opslaan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Save manually"
msgstr "Handmatig opslaan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr "Record opslaan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#, python-format
msgid "Scan barcode"
msgstr "Scan barcode"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings/settings_app.xml:0
#, python-format
msgid "Search"
msgstr "Zoek"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Search More..."
msgstr "Zoek meer..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#, python-format
msgid "Search a Model..."
msgstr "Zoek een model..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search a field..."
msgstr "Zoek een veld..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "Search for a command..."
msgstr "Zoek een opdracht..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "Search for a menu..."
msgstr "Zoek een menukaart..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr "Zoeken naar records"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#, python-format
msgid "Search..."
msgstr "Zoek..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Search: %s"
msgstr "Zoek: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr "Secundaire kleur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr "Bekijk details"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "See examples"
msgstr "Zie voorbeelden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Select"
msgstr "Selecteren"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""
"Selecteer <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Select Default"
msgstr "Selecteer standaard"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "Selecteer handtekeningstijl"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "Selecteer een model om een filter op te plaatsen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr "Weergave selecteren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Select all"
msgstr "Selecteer alle"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Select all records matching the search"
msgstr "Selecteer alle records die aan de zoekopdracht voldoen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Select field"
msgstr "Selecteer veld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Select records"
msgstr "Selecteer records"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Selected records"
msgstr "Geselecteerde regels"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/selection/selection_field.js:0
#, python-format
msgid "Selection"
msgstr "Selectie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Selection:"
msgstr "Selectie:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/email/email_field.xml:0
#: code:addons/web/static/src/views/fields/email/email_field.xml:0
#, python-format
msgid "Send Email"
msgstr "Verzend e-mail"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Set Defaults"
msgstr "Standaardwaarden instellen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "Omslagafbeelding instellen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr "Een kanban-status instellen..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr "Stel een prioriteit in..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "Stel een tijdzone in op je gebruiker"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
#, python-format
msgid "Set kanban state..."
msgstr "Kanbanstatus instellen..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#, python-format
msgid "Set priority..."
msgstr "Prioriteit instellen..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
#, python-format
msgid "Settings"
msgstr "Instellingen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr "Delen met alle gebruikers"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr "Sneltoetsen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Show sub-fields"
msgstr "Toon sub-velden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "Lokaal beschikbare modules worden weergegeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "Handtekening"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "Grootte:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr ""
"Er is iets gebeurd tijdens een poging om contact op te nemen met de server, "
"controleer of de server online is en of je nog steeds een werkende "
"netwerkverbinding hebt."

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
#, python-format
msgid "Something horrible happened"
msgstr "Er is iets verschrikkelijks gebeurd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Sort graph"
msgstr "Grafiek sorteren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Special:"
msgstr "Speciaal:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Stacked"
msgstr "Gestapeld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Start typing..."
msgstr "Start met typen..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/stat_info/stat_info_field.js:0
#, python-format
msgid "Stat Info"
msgstr "Stat-info"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
#, python-format
msgid "Status"
msgstr "Status"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#, python-format
msgid "Statusbar"
msgstr "Statusbalk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#, python-format
msgid "Stay Here"
msgstr "Hier blijven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#, python-format
msgid "Stay here"
msgstr "Hier blijven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "Nog steeds aan het laden..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Nog bezig met laden...<br />Even geduld aub."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "Stijl"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "Stijlen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Sun"
msgstr "Zo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "Ondersteuning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "Syntax fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "TIP"
msgstr "TIP"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Tags"
msgstr "Labels"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr "Neem even de tijd voor een kopje koffie,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""
"Neem een minuutje om koffie te halen,<br />want Odoo is aan het laden..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "BTW Nr."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Template:"
msgstr "Sjabloon:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/char/char_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Text"
msgstr "Tekst"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"Het BTW-nummer. Waarden hier worden gevalideerd op basis van het "
"landformaat. Je kunt '/' gebruiken om aan te geven dat de relatie niet "
"belastingplichtig is."

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""
"De inhoud van deze cel is te lang voor een XLSX-bestand (meer dan %s "
"tekens). Gebruik de CSV-indeling voor deze export."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr "Het veld is leeg. Er is niets om op te slaan."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""
"De bewerking is onderbroken. Dit betekent meestal dat de huidige bewerking "
"te lang duurt."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/utils/files.js:0
#, python-format
msgid "The selected file (%sB) is over the maximum allowed file size (%sB)."
msgstr ""
"Het geselecteerde bestand (%sB) overschrijdt de maximaal toegestane "
"bestandsgrootte (%sB)."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr ""
"Het geselecteerde bestand overschreed de maximum toegelaten grootte van %s."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""
"Het type van het veld '%s' moet een many2many veld zijn met een relatie naar"
" het 'ir.attachment' model."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid ""
"There are too many data. The graph only shows a sample. Use the filters to "
"refine the scope."
msgstr ""
"Er zijn te veel gegevens. De grafiek geeft alleen een deel weer. Gebruik de "
"filters om de weergave te verfijnen."

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""
"Er zijn te veel rijen (%s rijen, limiet: %s) om te exporteren als Excel "
"2007-2013 (.xlsx) -indeling. Overweeg de export te splitsen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "Er is geen afbeelding beschikbaar om in te stellen als omslagfoto."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Er is een probleem met het uploaden van je bestand."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/file_handler.js:0
#, python-format
msgid "There was a problem while uploading your file."
msgstr "Er is een probleem opgetreden bij het uploaden van je bestand."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.neutralize_banner
msgid "This database is neutralized."
msgstr "Deze database is geneutraliseerd."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr "Deze datum is in de toekomst. Ben je zeker dat je dit verwacht?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datepicker/datepicker.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr "Deze datum is in de toekomst. Ben je zeker dat je dit verwacht?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "Dit domein wordt niet ondersteund."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "This field is already first"
msgstr "Dit veld staat al op de eerste plaats"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "This field is already last"
msgstr "Dit veld is al laatste"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "Dit bestand is ongeldig. Selecteer een  afbeelding."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""
"Dit filter is voor iedereen en zal dus ook voor iedereen worden verwijderd "
"als je doorgaat."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "Dit is een voorbeeld van een externe rapportage."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "Dit is een voorbeeld van een intern rapport."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "This tag is already available"
msgstr "Dit label is al beschikbaar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr "Deze aanpassing past enkel records aan van de huidige pagina."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Thu"
msgstr "Do"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/float_time/float_time_field.js:0
#, python-format
msgid "Time"
msgstr "Tijd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""
"Tijdzone komt niet overeen: deze tijdzone verschilt van die van je browser.\n"
"Stel alsjeblieft dezelfde tijdzone in als je browser om tijdafwijkingen in je systeem te voorkomen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Today"
msgstr "Vandaag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/boolean_toggle/boolean_toggle_field.js:0
#, python-format
msgid "Toggle"
msgstr "Schakelen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#, python-format
msgid "Toggle menu"
msgstr "Menu wisselen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Tomorrow"
msgstr "Morgen"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr "Teveel items om weer te geven."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "Totaal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/views/fields/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr "Vertalen: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "Waar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""
"Probeer eens een paar records toe te voegen, of zorg ervoor dat er geen\n"
"                   filter in de zoekbalk is ingesteld."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Tue"
msgstr "Di"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#, python-format
msgid "Type a default text or press ENTER"
msgstr "Typ een standaardtekst of druk op ENTER"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Type your name to sign"
msgstr "Typ je naam om te ondertekenen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Type:"
msgstr "Soort:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/url/url_field.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""
"Wkhtmltopdf kon niet gevonden worden op het systeem. Dit rapport wordt "
"getoond in HTML."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Unable to save"
msgstr "Opslaan is niet mogelijk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Dearchiveren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Unarchive All"
msgstr "Dearchiveer alle"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr "Niet-gevangen CORS-fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr "Niet-afgevangen Javascript-fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr "Uncaught Promise"

#. module: web
#. odoo-javascript
#. odoo-python
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#, python-format
msgid "Undefined"
msgstr "Niet gedefinieerd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Unfold"
msgstr "Uitvouwen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""
"Onbekende CORS-fout\n"
"\n"
"Er is een onbekende CORS-fout opgetreden.\n"
"De fout is waarschijnlijk afkomstig van een JavaScript-bestand dat van een andere oorsprong afkomstig is.\n"
"(Als je je browserconsole opent, krijgt je mogelijk een hint over de fout.)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "Onbekend nonliteral type "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr "Rij verwijderen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr "Naamloos"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#, python-format
msgid "Unsaved changes"
msgstr "Niet-opgeslagen wijzigingen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "Geen titel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Update to:"
msgstr "Update naar:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade now"
msgstr "Upgrade nu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "Upgrade naar enterprise"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "Upgrade naar toekomstige versies"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Upload and Set"
msgstr "Uploaden en instellen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr "Upload je bestand"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Uploaded"
msgstr "Geupload"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Uploading"
msgstr "Uploaden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "Upload fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.js:0
#, python-format
msgid "Uploading error"
msgstr "Uploadfout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/file_handler.xml:0
#, python-format
msgid "Uploading..."
msgstr "Uploaden..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "Uploading... (%s%)"
msgstr "Uploaden... (%s%)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr "Gebruik dit voor mijn Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr "Standaard gebruiken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "Gebruiker"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr "Fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr "Bevestiging fout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Values"
msgstr "Waarden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#: code:addons/web/static/src/views/form/form_label.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr "De hier ingestelde waarden zijn bedrijfsspecifiek."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr "Verschil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr "Bekijk %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr "Toegangsrechten bekijken"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "Velden weergeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "View In Kanban"
msgstr "Kanban weergave"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "View Metadata"
msgstr "Metadata weergeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr "Recordregels bekijken"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "Soort weergave"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr "Bekijk de schakelaar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Warning"
msgstr "Waarschuwing"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr "Web Benchmarks"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "Web mobiel testen"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr "Web testen"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr "Website link"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Wed"
msgstr "Wo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Week"
msgstr "Week"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "Well Done!"
msgstr "Goed gedaan!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Widget:"
msgstr "Widget:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
#, python-format
msgid "Would you like to save your changes?"
msgstr "Wil je je wijzigingen opslaan?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/iframe_wrapper/iframe_wrapper_field.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr "Wrap onbewerkte html in een iframe"

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
#, python-format
msgid "Wrong login/password"
msgstr "Foutieve login/wachtwoord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Year"
msgstr "Jaar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr "Geel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Yes"
msgstr "Ja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Yesterday"
msgstr "Gisteren"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_successful
msgid "You are logged in."
msgstr "Je bent ingelogd."

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "Je bent niet gemachtigd om hier bijlages toe te voegen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""
"Je kunt de relaties niet volgen voor deze gekoppelde veld constructie."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "You do not have access to the model \"%s\"."
msgstr "Je hebtgeen toegang tot het model \"%s\"."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr "Je gelooft het misschien niet,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr ""
"Je zou het haast niet geloven,<br />maar Odoo is wel degelijk aan het "
"laden..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "You need to be able to edit parent first to add property tags"
msgstr ""
"Je moet eerst een bovenliggend veld kunnen bewerken om eigendomslabels toe "
"te voegen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "You need to be able to edit parent first to configure property fields"
msgstr ""
"Je moet eerst een bovenliggend veld kunen bewerken om eigenschapsvelden te "
"configureren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""
"Je moet dit nieuwe record opslaan voordat je de vertaling bewerkt. Wil je "
"doorgaan?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""
"Je moet Odoo starten met minimaal twee werkers om een PDF versie van de "
"rapportages te kunnen afdrukken."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""
"Je moet je versie van Wkhtmltopdf upgraden naar ten minste 0.12.0 om een "
"correcte weergave van kop- en voetteksten te krijgen, evenals ondersteuning "
"voor het breken van tabellen tussen pagina's."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr ""
"Jouw Odoo-sessie is verlopen. De huidige pagina wordt binnenkort vernieuwd."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""
"Jouw geïnstalleerde Wkhtmltopdf lijkt niet meer te werken. Het rapport wordt"
" getoond in html."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr "[Geen widget %s]"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "een dag geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "ongeveer 1 minuut geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "ongeveer 1 maand geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "ongeveer 1 jaar geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "ongeveer een uur geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "all"
msgstr "alle"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "alle records"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#, python-format
msgid "and"
msgstr "en"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "any"
msgstr "één van de"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "are valid for this update."
msgstr "zijn geldig voor deze update."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr "als een nieuwe"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr "op:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr "omdat Odoo aan het laden is..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr "maar de applicatie wordt daadwerkelijk geladen..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "onderliggende van"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "bevat"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr "datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "bevat niet"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "bevat niet"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr "downloaden..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr "bv. Global Business Solutions"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr "voor:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "groter dan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr "groter dan of gelijk aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr "hex"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "hour"
msgstr "uur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "hours"
msgstr "uren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "is"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is No"
msgstr "is Nee"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is Yes"
msgstr "is Ja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr "komt na"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr "komt na of is gelijk aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr "komt voor"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr "komt voor of is gelijk aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr "ligt tussen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "is gelijk aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "is niet"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "is niet ="

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "is niet gelijk aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr "is niet ingesteld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr "is ingesteld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr "Odoo is nog aan het laden..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/utils/numbers.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "kleiner dan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "minder dan een minuut geleden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr "kleiner dan of gelijk aan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "like"
msgstr "zoals"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "menus"
msgstr "menu's"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "minute"
msgstr "minuut"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "minutes"
msgstr "minuten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "more"
msgstr "meer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "new tab"
msgstr "nieuw tabblad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr "geen beschrijving gegeven"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "none"
msgstr "geen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr "niet"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "niet in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "not like"
msgstr "niet zoals"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "niet ingesteld (onwaar)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "onderstaande regels:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "van:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "on any screen to show shortcut overlays and"
msgstr "op elk scherm om de sneltoetsoverlappingen te tonen en"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "of"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "bovenliggende van"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr "query"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "record(s)"
msgstr "record(s)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "records ?"
msgstr "records?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "remaining)"
msgstr "resterend)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr "zoek"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "selected"
msgstr "geselecteerd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "selected records,"
msgstr "geselecteerde records,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr "ingesteld"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "ingesteld (waar)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "to trigger a shortcut."
msgstr "een sneltoets te activeren."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "type a default text or press ENTER"
msgstr "typ een standaardtekst of druk op ENTER"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "— press"
msgstr "— druk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#, python-format
msgid "— search for"
msgstr "— zoeken"
