# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <PERSON> <jonathan.<PERSON>.<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# l<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <robert.fry<PERSON><PERSON>@linserv.se>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <lovar<PERSON>@gmail.com>, 2024
# <PERSON><PERSON><PERSON>-<PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Jakob Krabbe <<EMAIL>>, 2025\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr " poster"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "# kodredigerare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d dagar sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d timmar sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d minuter sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d månader sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d år sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#, python-format
msgid "%s Files"
msgstr "%s filer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "%s days ago"
msgstr "%s dagar sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "%s records"
msgstr "%s uppgifter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' är inte ett giltigt datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr "'%s' är inte ett giltigt datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' är inte ett korrekt datum , datum/tid eller tid"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' är inte korrekt datum/tid"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' är inte en korrekt \"float\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' är inte ett korrekt heltal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "'%s' är inte ett giltigt valutafält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' är inte konverterbart till datum, datum/tid eller tid"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr "'%s' är inte synkroniserat med '%s'."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "(%s/%sMB)"
msgstr "(%s/%sMB)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "(Community Edition)"
msgstr "(Användarföreningens utgåva)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr "(växel)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(räkna)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(create)"
msgstr "(skapa)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#, python-format
msgid "(current)"
msgstr "(nuvarande)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr "(inget resultat)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "(no string)"
msgstr "(ingen sträng)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(ingen rubrik)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid ""
") format(\"woff\");\n"
"                        font-weight: normal;\n"
"                        font-style: normal;\n"
"                    }"
msgstr ""
") format(\"woff\");\n"
"font-weight: normal;\n"
"font-style: normal;\n"
"}"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "+ KEY"
msgstr "+ NYCKEL"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr "07/08/2020"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr "08/07/2020"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "1 record"
msgstr "1 post"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr "<span class=\"text-nowrap\">$ 2,887.50</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr "<span class=\"text-nowrap\">1,500.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr "<span class=\"text-nowrap\">2,350.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr "<span class=\"text-nowrap\">15% moms</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                       Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                        Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">Tax 15%</span>"
msgstr "<span id=\"line_tax_ids\">15% skatt</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr "<span itemprop=\"name\">Deco Addict</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.00</span>"
msgstr "<span>5.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr "<span>Belopp</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>Beskrivning</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2023/00003</span>"
msgstr ""
"<span>Faktura</span>\n"
"                            <span>INV/2023/00003</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr "<span>Betalvillkor: 30 dagar</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>Antal</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>Moms</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>Enhetspris</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Förfallodatum:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Fakturadatum:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>Totalt</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Untaxed Amount</strong>"
msgstr "<strong>Otaxerat belopp</strong>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "=ilike"
msgstr "=icke liknande"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "=like"
msgstr "=liknande"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid ""
"@font-face {\n"
"                        font-family: \"font\";\n"
"                        src: url(data:font/ttf;base64,"
msgstr ""
"@font-face {\n"
"font-family: \"font\";\n"
"src: url(data:font/ttf;base64,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr "Ett filter med samma namn finns redan."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr "Ett namn för ditt favoritfilter krävs."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"Ett popup-fönster är blockerat. Ändra dina webblästarinställningar för att "
"tillåta popup-fönster för denna sida."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "ALLA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "NÅGON"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "Åtkomst nekad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr "Åtkomstfel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Tillgång till alla företagsappar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/ace/ace_field.js:0
#, python-format
msgid "Ace Editor"
msgstr "Ace-redigerare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/views/form/status_bar_buttons/status_bar_buttons.xml:0
#, python-format
msgid "Action"
msgstr "Åtgärd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Action ID:"
msgstr "Aktivitets ID:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Vy för åtgärdsfönster"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "Aktivera tillgångsfelsökning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr "Aktivera felsökning på testtillgångar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode (with assets)"
msgstr "Aktivera felsökningsläge (med tillgångar)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode"
msgstr "Aktivera utvecklarmod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with assets)"
msgstr "Aktivera utvecklarmod (med resurser)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with tests assets)"
msgstr "Aktivera utvecklarmod (med tests resurser)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
#, python-format
msgid "Add"
msgstr "Lägg till"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "Anpassat filter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "Lägg till gruppering"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
#, python-format
msgid "Add a Property"
msgstr "Lägg till egenskap"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Add a Value"
msgstr "Lägg till ett värde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "Lägg till ett villkor"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Add a line"
msgstr "Lägg till rad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "Lägg till gren"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "Add column"
msgstr "Lägg till kolumn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "Lägg till filter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "Lägg till ett nytt värde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "Lägg till nod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr "Lägg till qweb-direktivkontext"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "Lägg till etikett"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "Lägg till i favoriter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "Lägg till: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#, python-format
msgid "Add: %s"
msgstr "Lägg till: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#, python-format
msgid "Additionnal actions"
msgstr "Ytterligare åtgärder"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt & Sign"
msgstr "Adoptera & Signera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "Infoga din signatur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "Varning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "All"
msgstr "Alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#, python-format
msgid "All day"
msgstr "Hela dagen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "Alla användare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Among the"
msgstr "Bland de"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#, python-format
msgid "An error occured while uploading."
msgstr "Ett fel uppstod under uppladdningen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr "Ett fel uppstod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "And more"
msgstr "Lägg till fler"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "Någon"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr ""
"Visas som standard i det övre högra hörnet på dina utskrivna dokument "
"(rapporthuvudet)."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#: code:addons/web/static/src/views/fields/daterange/daterange_field.js:0
#, python-format
msgid "Apply"
msgstr "Verkställ"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Arkiv"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Archive All"
msgstr "Arkivera alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "Är du säker på att du vill arkivera alla poster från denna kolumn?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "Är du säker på att du vill arkivera alla valda poster?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "Är du säker på att du vill arkivera den här posten?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Är du säker på att du vill ta bort den här kolumnen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Är du säker på att du vill ta bort det här filtret?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr "Är du säker på att du vill radera dessa poster?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "Är du säker på att du vill radera de här posterna?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Are you sure you want to delete this column?"
msgstr "Är du säker på att du vill ta bort den här kolumnen?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid ""
"Are you sure you want to delete this property field? It will be removed for "
"everyone using the \"%s\" %s."
msgstr ""
"Är du säker på att du vill ta bort detta egenskapsfält? Det kommer att tas "
"bort för alla som använder \"%s\" %s."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Är du säker på att du vill radera denna post?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Är du säker på att du vill radera denna post?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "Är du säker på att du vill utföra följande uppdatering på dem"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "As a default text when no value are set"
msgstr "Som en standardtext när inget värde har ställts in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Ascending"
msgstr "Stigande"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Attach"
msgstr "Bifoga"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Attachment"
msgstr "Bilaga"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.js:0
#, python-format
msgid "Attachment Image"
msgstr "Bild på bifogad fil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "Auto"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Available fields"
msgstr "Tillgängliga fält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr "Bakgrundsbild"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#: code:addons/web/static/src/views/fields/badge/badge_field.js:0
#, python-format
msgid "Badge"
msgstr "Emblem"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/badge_selection/badge_selection_field.js:0
#, python-format
msgid "Badges"
msgstr "Emblem"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Bar Chart"
msgstr "Stapeldiagram"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Bas"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr "Bli superanvändare"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""
"Binära fält kan inte exporteras till Excel om inte deras innehåll är "
"base64-kodat. Det verkar inte vara fallet för %s."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
#, python-format
msgid "Binary file"
msgstr "Binärfil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "Buggfix garanti"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Button"
msgstr "Knapp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Button Type:"
msgstr "Typ av knapp:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Genom att klicka på Adopt & Sign godkänner jag att den valda "
"signaturen/initialerna kommer att vara en giltig elektronisk representation "
"av min handskrivna signatur/initialer för alla ändamål när den används på "
"dokument, inklusive juridiskt bindande avtal."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "Bytes"
msgstr "Bytes"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "CLEAR"
msgstr "RENSA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "CMD"
msgstr "CMD"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "CTRL"
msgstr "CTRL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/phone/phone_field.xml:0
#, python-format
msgid "Call"
msgstr "Samtal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/daterange/daterange_field.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "Avbryt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr "Avbryt uppladdning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "Kortfärg: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Change default:"
msgstr "Byt standardvärde:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Change graph"
msgstr "Byt graf"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#: code:addons/web/static/src/views/fields/boolean/boolean_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Checkbox"
msgstr "Kryssruta"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_checkboxes/many2many_checkboxes_field.js:0
#, python-format
msgid "Checkboxes"
msgstr "Kryssrutor"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr "Välj"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr "Välj fil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr "Välj ett felsökningskommando..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Clear"
msgstr "Töm"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "Ta bort signatur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Close"
msgstr "Stäng"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#, python-format
msgid "Close menu"
msgstr "Stäng meny"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr "Färger"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "Kolumn %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#: model:ir.model,name:web.model_res_company
#, python-format
msgid "Companies"
msgstr "Bolag"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "Bolag"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr "Företagsuppgifter"

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr "Företagets dokumentlayout"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr "Företagslogo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "Företagsnamn"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr "Företagets slogan"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "Företagsnamn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr "Jämförelse"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "Tillstånd:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr "Konfigurera er dokumentlayout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Bekräftelse"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr "Anslutningen förlorades. Försöker återansluta..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr "Anslutningen återställd. Du är tillbaka online."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Context:"
msgstr "Sammanhang:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "Kontrollpanelens knappar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copied"
msgstr "Kopierad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr "Kopierad!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy"
msgstr "Kopiera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy Multiline Text to Clipboard"
msgstr "Kopiera flerradig text till Urklipp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy Text to Clipboard"
msgstr "Kopiera text till klippbord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy URL to Clipboard"
msgstr "Kopiera URL till klippbord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "Kopiera hela felet till urklipp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "Kopiera till urklipp"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr "Copyright &amp;copy;"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "Copyright © 2004"
msgstr "Upphovsrätt © 2004"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Correct issues to save, or discard changes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "Kunde inte ansluta till servern"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
#, python-format
msgid "Could not display the selected image"
msgstr "Kunde inte visa den valda bilden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
#, python-format
msgid "Could not display the selected pdf"
msgstr "Kunde inte visa den valda pdf:en"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr "Det gick inte att visa den angivna bildadressen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "Kunde inte serialisera XML"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""
"Det gick inte att ange omslagsbilden: felaktigt fält (\"%s\") är angivet i "
"vyn."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "Could not start scanning. "
msgstr "Kunde inte starta skanningen. "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/utils.js:0
#, python-format
msgid "Count"
msgstr "Antal"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "Land"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Create"
msgstr "Skapa"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "Skapa "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create \"%s\""
msgstr "Skapa \"%s\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Skapa \"<strong>%s</strong>\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create %s"
msgstr "Skapa %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "Create <strong>%s</strong> as a new %s?"
msgstr "Skapa <strong>%s</strong> som en ny %s?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "Skapa och redigera..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create and edit..."
msgstr "Skapa och redigera..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
#, python-format
msgid "Create record"
msgstr "Skapa post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr "Skapa: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "Skapad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "Datum skapad:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "Skapa Användare:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Cumulative"
msgstr "Kumulativ"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "Nuvarande tillstånd"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr "Anpassade färger"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr "Mörkblå"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr "Mörklila"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Databas"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/date/date_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Date"
msgstr "Datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Date & Time"
msgstr "Datum & tid"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Day"
msgstr "Dag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr "Inaktivera felsöknings läge"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Deactivate the developer mode"
msgstr "Inaktivera utvecklarmod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr "Felsöknings verktyg..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Decimal"
msgstr "Decimal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "Standard"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Default State"
msgstr "Standardtillstånd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Default Value"
msgstr "Standardvärde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Default text is used when no values are set"
msgstr "Standardtext används när inga värden anges"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Default value"
msgstr "Standardvärde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Default:"
msgstr "Förvalt:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_tags/tags_list.xml:0
#: code:addons/web/static/src/views/fields/many2many_tags/tags_list.xml:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Delete"
msgstr "Ta bort"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Delete Property Field"
msgstr "Ta bort egenskapsfält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr "Radera sak"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr "Radera nod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Delete row"
msgstr "Ta bort rad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "Radera rad "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Descending"
msgstr "Fallande"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Developer Tools"
msgstr "Utvecklarverktyg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
#, python-format
msgid "Different currencies cannot be aggregated"
msgstr "Olika valutor kan inte aggregeras"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#, python-format
msgid "Discard"
msgstr "Avbryt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Discard changes"
msgstr "Släng bort ändringar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr "Kasera post"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr "Vill du verkligen avbryta uppladdningen av %s?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "Vill du verkligen ta bort den här exportmallen?"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr "Dokumentmall"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr "Dokumentation"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Domain"
msgstr "Domän"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr "Domännod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr "Domänen är inte korrekt utformad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr "Domänen stöds inte"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Domain:"
msgstr "Domän:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr "Gå inte ännu,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Lämna inte skärmen än,<br />det laddar fortfarande..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Download"
msgstr "Ladda ner"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr "Ladda ner PDF -förhandsgranskning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Download xlsx"
msgstr "Ladda ner xslx"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "Rita"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Rullgardinsmeny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "Kopiera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
#, python-format
msgid "Edit"
msgstr "Redigera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "Redigera åtgärd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "Redigera kolumn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Edit Domain"
msgstr "Redigera domän"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit SearchView"
msgstr "Ändra sökvy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Edit Stage"
msgstr "Redigera etapp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr "Editera vy: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr "Editera post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Edit: %s"
msgstr "Redigera: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/email/email_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "E-post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Empty email address"
msgstr "Tom e-postadress"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr "Aktivera profilering"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Enter e-mail address"
msgstr "Ange e-postadress"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/highlight_text/form_label_highlight_text.xml:0
#, python-format
msgid "Enterprise"
msgstr "Företag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Entry Count"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "Error"
msgstr "Fel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "Esc to discard"
msgstr "Esc för att avbryta"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "Allas kalendrar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "Allt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Expand all"
msgstr "Expandera alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Export"
msgstr "Exportera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Export All"
msgstr "Exportera alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Export Data"
msgstr "Exportera data"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Export Format:"
msgstr "Exportformat:"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr "Exportering av grupperad data till csv stöds inte."

#. module: web
#. odoo-javascript
#. odoo-python
#: code:addons/web/controllers/export.py:0
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "External ID"
msgstr "Externt ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "Extern länk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#, python-format
msgid "FILTER"
msgstr "FILTER"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr "Det gick inte att utvärdera sökkontext"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "Falskt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_favorite/boolean_favorite_field.js:0
#, python-format
msgid "Favorite"
msgstr "Favorit"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "Favoriter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Field Type"
msgstr "Fälttyp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Field:"
msgstr "Fält:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Fields to export"
msgstr "Fält som skall exporteras"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/binary/binary_field.js:0
#, python-format
msgid "File"
msgstr "Fil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "Filuppladdning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "Filter med samma namn finns redan."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "Filter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Flip axis"
msgstr "Vänd axel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
#, python-format
msgid "Float"
msgstr "Flyttal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Fold"
msgstr "Vik ihop"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "Följd av"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#, python-format
msgid "Followed-by"
msgstr "Efterföljd av"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr "Typsnitt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/font_selection/font_selection_field.js:0
#, python-format
msgid "Font Selection"
msgstr "Val av teckensnitt"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr "Sidfot"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "Text som visas i sidfoten på alla rapporter."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"För Excel-kompatibilitet kan data inte exporteras om det finns mer än 16384 "
"kolumner. Tips: försök att vända axeln, filtrera ytterligare eller minska "
"antalet mått."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "Formulär"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr "Fri Vy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Fri"
msgstr "Fre"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#, python-format
msgid "Fuchsia"
msgstr "Fuchsia"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "Fullständigt namn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr "Fushia"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "GNU LGPL Licensed"
msgstr "GNU LGPL Licensierad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Get View"
msgstr "Hämta vy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "Få den här funktionen och mycket mer med Odoo Enterprise!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/url/url_field.xml:0
#: code:addons/web/static/src/views/fields/url/url_field.xml:0
#, python-format
msgid "Go to URL"
msgstr "Gå till URL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "Diagram"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "Grön"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#, python-format
msgid "Group By"
msgstr "Gruppera efter"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-rutt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/handle/handle_field.js:0
#, python-format
msgid "Handle"
msgstr "Hantera"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "Rubriktexten visas överst i alla rapporter."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#, python-format
msgid ""
"Heads up! Your recent changes are too large to save automatically. Please "
"click the <i class=\"fa fa-cloud-upload fa-fw\"></i> button now to ensure "
"your work is saved before you exit this tab."
msgstr ""
"Var uppmärksam! Dina senaste ändringar är för stora för att sparas "
"automatiskt. Klicka på knappen <i class=\"fa fa-cloud-upload fa-fw\"></i> nu"
" för att se till att ditt arbete sparas innan du lämnar den här fliken."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "Göm i Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.xml:0
#, python-format
msgid "Hide in kanban"
msgstr "Dölj i Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr "Tryck DOWN för att navigera till listan nedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "Tryck ENTER för"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "Tryck ENTER för att SKAPA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "Tryck ENTER för att SPARA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "Tryck ESC för att FÖRKASTA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "Jag är säker på det här."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "Jag vill uppdatera data (import-kompatibel export)"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr ""
"Om du ändrar %s eller %s, tillämpas synkroniseringen igen och data kommer "
"att ändras."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.xml:0
#, python-format
msgid "Image"
msgstr "Bild"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "In %s days"
msgstr "Om %s dagar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/integer/integer_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Integer"
msgstr "Heltal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#, python-format
msgid "Internal link"
msgstr "Intern länk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr "Intervall"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Invalid data"
msgstr "Ogiltig data"

#. module: web
#. odoo-python
#: code:addons/web/controllers/database.py:0
#: code:addons/web/controllers/database.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""
"Ogiltigt databasnamn. Endast alfanumeriska tecken, understreck, bindestreck "
"och punkter är tillåtna."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Invalid domain"
msgstr "Ogiltig Domän"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr "Ogiltig fältkedja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr ""
"Ogiltig fältkedja. Du kan ha använt ett icke-existerande fältnamn eller "
"följt ett icke-relationellt fält."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "Invalid fields"
msgstr "Ogiltiga fält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr "Ogiltiga fält:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "Invalid fields: "
msgstr "Ogiltiga fält: "

#. module: web
#. odoo-python
#: code:addons/web/controllers/domain.py:0
#, python-format
msgid "Invalid model: %s"
msgstr "Ogiltig modell: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Invite"
msgstr "Bjud in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Invite New Users"
msgstr "Bjud in nya användare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Inviting..."
msgstr "Bjuder in..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__is_company_details_empty
msgid "Is Company Details Empty"
msgstr "Is Företagsuppgifter Tom"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""
"Det är möjligt att \"t-call\"-tiden inte motsvarar den totala tiden för "
"mallen. Eftersom den globala tiden (i rullgardinsmenyn) inte tar hänsyn till"
" den tid som inte ingår i renderingen (leta efter mallen, läsa, ärva, "
"sammanställa...). Under rendering tar den globala tiden också en del av "
"tiden för att göra profilen samt en del som inte loggas i den funktion som "
"genereras av qweb."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.js:0
#, python-format
msgid "Kanban Examples"
msgstr "Kanban Exempel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr "Kanban: ingen åtgärd för typ: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/label_selection/label_selection_field.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
#, python-format
msgid "Label Selection"
msgstr "Val av etikett"

#. module: web
#. odoo-python
#: code:addons/web/controllers/session.py:0
#, python-format
msgid "Languages"
msgstr "Språk"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "Senast redigerad den"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad på"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "Senaste ändringsdatum:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "Senast ändrad av:"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr "Layout"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr "Layout bakgrund"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "Lämna Utvecklar Verktygen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr "Ljusblå"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Line Chart"
msgstr "Linjediagram"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "Lista"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "Ladda"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Load demo data"
msgstr "Ladda demodata"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Load everything anyway."
msgstr "Ladda allt i alla fall."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Load more... ("
msgstr "Ladda mer...("

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "Laddar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "Laddar, vänligen vänta..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Loading..."
msgstr "Laddar..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Logga in"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "Logga in som superanvändare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: model_terms:ir.ui.view,arch_db:web.login_successful
#, python-format
msgid "Log out"
msgstr "Logga ut"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logotyp"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr "Logo Primär Färg"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr "Logo Sekundär Färg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr "MailLeveransFel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Main actions"
msgstr "Huvudåtgärder"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Manage Attachments"
msgstr "Hantera Bilagor"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Administrera databaser"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "Hantera filter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one_barcode/many2one_barcode_field.js:0
#, python-format
msgid "Many2OneBarcode"
msgstr "Många2EnBarcode"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Many2many"
msgstr "Many2many"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Many2one"
msgstr "Many2one"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "Match"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "Sök efter poster med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "Sök efter poster med detta urval:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""
"Du kanske skulle överväga att återladda applikationen genom att trycka F5..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr "Mått"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr "Mellanblå"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr "Mötesämne"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr "Mötes Ärende:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: model:ir.model,name:web.model_ir_ui_menu
#, python-format
msgid "Menu"
msgstr "Meny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Method:"
msgstr "Metod:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr "Saknad Post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Mobile support"
msgstr "Mobil support"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Model"
msgstr "Modell"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr "Model Postregler"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Model:"
msgstr "Modell:"

#. module: web
#: model:ir.model,name:web.model_ir_model
msgid "Models"
msgstr "Modeller"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Modifiers:"
msgstr "Modifierare:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Mon"
msgstr "Mån"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/monetary/monetary_field.js:0
#, python-format
msgid "Monetary"
msgstr "Monetär"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Month"
msgstr "Månad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#: code:addons/web/static/src/views/form/button_box/button_box.xml:0
#, python-format
msgid "More"
msgstr "Mer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
#, python-format
msgid "Move to %s..."
msgstr "Flytta till%s..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/text/text_field.js:0
#, python-format
msgid "Multiline Text"
msgstr "Flerradig Text"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr "Mitt Odoo.com-konto"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr "INGEN"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "New"
msgstr "Ny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr "Ny %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "New Event"
msgstr "Nytt evenemang"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
#, python-format
msgid "New Property"
msgstr "Ny egenskap"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "New design"
msgstr "Ny design"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "New template"
msgstr "Ny mall"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "New:"
msgstr "Ny:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "New: %s"
msgstr "Ny: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#, python-format
msgid "Next"
msgstr "Nästa"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr "Nästa sida"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "No"
msgstr "Nej"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_value.js:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#, python-format
msgid "No Access"
msgstr "Ingen åtkomst"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr "Ingen Uppdatering:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr "Ingen åtgärd med id '%s' hittades"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr "Ingen färg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "No command found"
msgstr "Inte kommando hittades"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "Ingen data"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr "Ingen data att visa"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No debug command found"
msgstr "Inget avlusningskommando hittades"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "No device can be found."
msgstr "Ingen enhet kunde hittas."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "No match found."
msgstr "Ingen träff hittad."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr "Ingen meny hittad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "No records"
msgstr "Poster saknas"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "Inga poster hittade!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "No result"
msgstr "Inget resultat"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No result found"
msgstr "Inga resultat hittade"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "No valid record to save"
msgstr "Ingen giltig post att spara"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr "Ingen vy av typen '%s' kunde hittas i den aktuella åtgärden."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "None"
msgstr "Inga"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "Inte satt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr "Inte aktiv status"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "Inte aktiv status, klicka för att ändra"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Object:"
msgstr "Objekt:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo Appar är snart tillgängliga"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "Odoo Klient Fel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr "Odoo Fel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr "Odoo Nätverks Fel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "Odoo S.A."
msgstr "Odoo S.A."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr "Odoo Server Fel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "Odoo Sessionen Förfallen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo Varning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "Odoo needs your authorization first."
msgstr "Odoo behöver din auktorisering först."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr ""
"Av de valda %d posterna har endast de första %darkiverats/oarkiverats."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#, python-format
msgid "Oh snap!"
msgstr "Åh, sjutton också!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "Vid ändring:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr "One2many"

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr ""
"Endast anställda har tillgång till denna databas. Vänligen kontakta "
"administratören."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr "Endast de första %d posterna har raderats (av %d valda)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Only the first %s records have been deleted (out of %s selected)"
msgstr "Endast de första %s posterna har raderats (av %s valda)"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""
"Endast typer %(supported_types)s stöds för kategori (hittad typ "
"%(field_type)s)"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""
"Endast typer %(supported_types)s stöds för filter (hittad typ "
"%(field_type)s)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "Bara du"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr "Öppna Vy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.xml:0
#, python-format
msgid "Open developer tools"
msgstr "Öppna utvecklingsverktyg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Open:"
msgstr "Öppna:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "Öppna: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Open: %s"
msgstr "Öppna: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Option Name"
msgstr "Alternativets namn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr "Valbara kolumner"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr "Orange"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#: code:addons/web/static/src/views/kanban/kanban_model.js:0
#, python-format
msgid "Other"
msgstr "Annat"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
#, python-format
msgid "PDF Viewer"
msgstr "PDF Förhandsvisare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr "PDF kontroller"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF fil"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"Sida:\n"
"                    <span class=\"page\"/>\n"
"                    av\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "Sida: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr "Sökare"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr "Pappersformat"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "Partner"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Lösenord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Pending Invitations:"
msgstr "Väntande inbjudningar:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/percent_pie/percent_pie_field.js:0
#, python-format
msgid "PercentPie"
msgstr "ProcentPie"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/percentage/percentage_field.js:0
#, python-format
msgid "Percentage"
msgstr "Procentandel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "Procent Kaka"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/phone/phone_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr "Välj en färg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Pie Chart"
msgstr "Tårtdiagram"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Cirkeldiagram kan inte blanda positiva och negativa tal. Försök att ändra "
"din domän för att bara visa positiva resultat"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "Pivot"

#. module: web
#. odoo-python
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr "Pivot %(title)s (%(model_name)s)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Pivot settings"
msgstr "Pivot-inställningar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr "Vänligen ha tålamod."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "Vänligen klicka på \"spara\" kanappen först"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Please complete your properties before adding a new one"
msgstr "Vänligen slutför dina egenskaper innan du lägger till en ny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr "Vänligen ange ett numeriskt värde"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "Vänligen namnge fältlistan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr "Vänligen spara före du bifogar en fil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "Vänligen välj fält att exportera..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "Vänligen välj fält att exportera som lista..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "Vänligen uppdatera översättningen av:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""
"Vänligen använd Kopiera knappen för att rapportera ditt felmeddelande till "
"din support."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2023/00003</span></b>"
msgstr ""
"Vänligen använd följande kommunikation för din betalning: <b><span> "
"INV/2023/00003</span></b>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "Please, scan again !"
msgstr "Snälla, skanna igen!"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "Drivs med %s%s"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Drivs med <span>Odoo</span>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "Inställningar"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr "Förhandsgranskning"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "Förhandsvisa Extern Rapport"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "Förhandsvisa Intern Rapport"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr "Förhandsvisa logo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#, python-format
msgid "Previous"
msgstr "Föregående"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr "Föregående Period"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr "Föregående År"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr "Föregående Meny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr "Föregående sida"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr "Primär färg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
#, python-format
msgid "Print"
msgstr "Skriv ut"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#, python-format
msgid "Printing options"
msgstr "Alternativ för utskrift"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.xml:0
#, python-format
msgid "Priority"
msgstr "Prioritet"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "Processing..."
msgstr "Bearbetar..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
#, python-format
msgid "Progress Bar"
msgstr "Förloppsindikator"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Properties"
msgstr "Egenskaper"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Property %s"
msgstr "Egenskap %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Property Name"
msgstr "Namn på egenskapen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr "Lila"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr "Q1"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr "Q2"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr "Q3"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr "Q4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr "Kvartal"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Quick add"
msgstr "Snabb lägg till"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Quick search: %s"
msgstr "Snabb sök: %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr "Qweb fältbild"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr "RGB"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr "RGBA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/radio/radio_field.js:0
#, python-format
msgid "Radio"
msgstr "Radio"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr "Post qweb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr "Post sql"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr "Post spår"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Recording..."
msgstr "Spelar in..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr "Röd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/reference/reference_field.js:0
#, python-format
msgid "Reference"
msgstr "Referens"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Refresh"
msgstr "Ladda om"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr "Paket med tillgångar från Regenerate"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Relation not allowed"
msgstr "Relation inte tillåten"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "Relation att följa"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Relation:"
msgstr "Relation:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#, python-format
msgid "Relational table"
msgstr "Relationell tabell"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Remaining Days"
msgstr "Återstående dagar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#, python-format
msgid "Remove"
msgstr "Ta bort"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Remove Cover Image"
msgstr "Ta bort omslagsbild"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Remove Property"
msgstr "Ta bort egenskap"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Remove field"
msgstr "Ta bort fält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "Ta bort från favoriter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "Avlägsna tag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "Tag bort denna favorit från listan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "Rapport"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "Sidfot i rapport"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr "Rapportlayout"

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr "Rapport Layout Förhandsvisning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr "Begär timeout"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#, python-format
msgid "Reset domain"
msgstr "Återställ domän"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr "Återställ till logo färger"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "Kör Klicka Överallt Test"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "Kör JS Mobil Tester"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr "Kör JS-tester"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#, python-format
msgid "SEE RESULT"
msgstr "SE RESULTAT"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
#, python-format
msgid "SIGNATURE"
msgstr "SIGNATUR"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr "Laxrosa"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Sat"
msgstr "Lör"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "Spara"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#, python-format
msgid "Save & Close"
msgstr "Spara & stäng"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#, python-format
msgid "Save & New"
msgstr "Spara & ny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "Spara som :"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Save as:"
msgstr "Spara som:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr "Spara aktuell sökning"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "Spara standardvärden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Save manually"
msgstr "Spara manuellt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr "Spara post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#, python-format
msgid "Scan barcode"
msgstr "Skanna streckkod"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings/settings_app.xml:0
#, python-format
msgid "Search"
msgstr "Sök"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Search More..."
msgstr "Sök flera..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#, python-format
msgid "Search a Model..."
msgstr "Sök efter en modell..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search a field..."
msgstr "Sök i ett fält..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "Search for a command..."
msgstr "Sök efter ett kommando..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "Search for a menu..."
msgstr "Sök efter en meny..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr "Sök efter post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#, python-format
msgid "Search..."
msgstr "Sök..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Search: %s"
msgstr "Sök: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr "Sekundär färg"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr "Visa detaljer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "See examples"
msgstr "Visa exempel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Select"
msgstr "Välj"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""
"Välj <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Select Default"
msgstr "Välj standard"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "Välj Signatur Stil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "Välj en modell för att lägga till ett filter."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr "Välj en vy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Select all"
msgstr "Välj alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Select all records matching the search"
msgstr "Välj alla poster som matchar sökningen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Select field"
msgstr "Välj fält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Select records"
msgstr "Välj uppgifter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Selected records"
msgstr "Valda poster"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/selection/selection_field.js:0
#, python-format
msgid "Selection"
msgstr "Urval"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Selection:"
msgstr "Val:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/email/email_field.xml:0
#: code:addons/web/static/src/views/fields/email/email_field.xml:0
#, python-format
msgid "Send Email"
msgstr "Skicka e-post"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Set Defaults"
msgstr "Ställ in standardvärden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "Sätt En Omslagsbild"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr "Ställ in en kanbanstatus..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr "Sätt en prioritet..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "Sätt en tidszon på din användare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
#, python-format
msgid "Set kanban state..."
msgstr "Ange kanbanstatus..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#, python-format
msgid "Set priority..."
msgstr "Sätt prioritet..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
#, python-format
msgid "Settings"
msgstr "Inställningar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr "Dela med alla användare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr "Genvägar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Show sub-fields"
msgstr "Visa sub-fält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "Visar lokalt tillgängliga moduler"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "Signatur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "Storlek:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr ""
"Något hände när du försökte kontakta servern, kontrollera att servern är "
"online och att du fortfarande har en fungerande nätverksanslutning."

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
#, python-format
msgid "Something horrible happened"
msgstr "Någonting hemskt hände"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Sort graph"
msgstr "Sortera graf"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Special:"
msgstr "Special:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Stacked"
msgstr "Stackad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Start typing..."
msgstr "Börja skriva..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/stat_info/stat_info_field.js:0
#, python-format
msgid "Stat Info"
msgstr "Statlig information"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
#, python-format
msgid "Status"
msgstr "Status"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#, python-format
msgid "Statusbar"
msgstr "Statusfält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#, python-format
msgid "Stay Here"
msgstr "Stanna här"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#, python-format
msgid "Stay here"
msgstr "Stanna här"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "Laddar fortfarande..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Laddar fortfarande...<br />Ha tålamod."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "Stil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "Stilar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Sun"
msgstr "Sön"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "Stöd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "Syntax error"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "TIP"
msgstr "TIP"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Tags"
msgstr "Etiketter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr "Ta en minut och hämta kaffe,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Ta en paus för en kaffe,<br/>därför den laddar..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "Moms-ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Template:"
msgstr "Mall:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/char/char_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Text"
msgstr "Text"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"Skatteidentifikationsnummer. Värden här kommer att valideras baserat på "
"landsformatet. Du kan använda '/' för att ange att partnern inte är "
"skattepliktig."

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""
"Innehållet i den här cellen är för långt för en XLSX -fil (mer än %s "
"tecken). Använd CSV -formatet för denna export."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr "Fältet är tomt, det finns ingenting att spara."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""
"Operationen avbröts. Detta innebär vanligtvis att den aktuella operationen "
"tar för lång tid."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/utils/files.js:0
#, python-format
msgid "The selected file (%sB) is over the maximum allowed file size (%sB)."
msgstr ""
"Den valda filen (%sB) är större än den maximalt tillåtna filstorleken (%sB)."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "Vald fil överstiger maximal filstorlek på %s."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""
"Denna typ av fält '%s' måste var ett many2many fält med en relation  till "
"'ir.attachment' modellen."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid ""
"There are too many data. The graph only shows a sample. Use the filters to "
"refine the scope."
msgstr ""
"Det finns för mycket data. Grafen visar bara ett prov. Använd filtren för "
"att förfina omfattningen."

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""
"Det finns för många rader ( %s rader, gräns: %s) för att exporteras som "
"Excel 2007-2013 (.xlsx)-format. Överväg att dela upp exporten."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "Det finns ingen bild tillgänglig att använda som omslag."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Det uppstod ett fel vid filuppladdningen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/file_handler.js:0
#, python-format
msgid "There was a problem while uploading your file."
msgstr "Det uppstod ett problem när filen skulle laddas upp."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.neutralize_banner
msgid "This database is neutralized."
msgstr "Denna databas är neutraliserad."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr "Detta datum ligger i framtiden. Se till att detta är vad du vill."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datepicker/datepicker.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""
"Detta datum ligger i framtiden. Se till att detta är vad du förväntade dig."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "Den här domänen stöds inte."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "This field is already first"
msgstr "Det här fältet är redan först"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "This field is already last"
msgstr "Det här fältet är redan sist"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "Felaktig fil. Vänligen välj en bild."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""
"Detta filter är globalt och kommer att tas bort för alla om du fortsätter."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "Detta är ett exempel på en extern rapport."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "Detta är ett exempel på en intern rapport."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "This tag is already available"
msgstr "Den här taggen är redan tillgänglig"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr ""
"Denna uppdatering tar endast hänsyn till posterna på den aktuella sidan."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Thu"
msgstr "Tor"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/float_time/float_time_field.js:0
#, python-format
msgid "Time"
msgstr "Tid"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""
"Tidszonmatchning: Denna tidszon skiljer sig från din webbläsares.\n"
"Ställ in samma tidszon som din webbläsares för att undvika tidsavvikelser i ditt system."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Today"
msgstr "Idag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/boolean_toggle/boolean_toggle_field.js:0
#, python-format
msgid "Toggle"
msgstr "Växla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#, python-format
msgid "Toggle menu"
msgstr "Växla meny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Tomorrow"
msgstr "Imorgon"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr "För många objekt att visa."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "Totalt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/views/fields/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr "Översätt: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "Sant"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""
"Försök att lägga till några poster, eller se till att det inte finns något\n"
"                    aktivt filter i sökfältet."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Tue"
msgstr "Tis"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#, python-format
msgid "Type a default text or press ENTER"
msgstr "Skriv in en standard-text eller tryck på ENTER"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Type your name to sign"
msgstr "Skriv in ditt namn för att signera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Type:"
msgstr "Typ:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/url/url_field.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""
"Det gick inte att hitta Wkhtmltopdf på det här systemet. Rapporten kommer "
"att visas i html."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Unable to save"
msgstr "Går inte att spara"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Avarkivera"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Unarchive All"
msgstr "Avarkivera alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr "Uncaught CORS Error"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr "Uncaught Javascript Error"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr "Uncaught Promise"

#. module: web
#. odoo-javascript
#. odoo-python
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#, python-format
msgid "Undefined"
msgstr "Odefinierad"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Unfold"
msgstr "Veckla ut"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "Okänd icke -bokstavstyp "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr "Radera rad "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr "Namnlös"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#, python-format
msgid "Unsaved changes"
msgstr "Osparade ändringar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "Namnlös"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Update to:"
msgstr "Uppdatera till:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade now"
msgstr "Uppgradera nu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "Uppgradera till enterprise"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "Uppgradera till senare version"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Upload and Set"
msgstr "Överför och ställ in"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr "Ladda upp din fil"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Uploaded"
msgstr "Överförd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Uploading"
msgstr "Laddar upp"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "Överföringsfel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.js:0
#, python-format
msgid "Uploading error"
msgstr "Uppladdningsfel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/file_handler.xml:0
#, python-format
msgid "Uploading..."
msgstr "Överför…"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "Uploading... (%s%)"
msgstr "Laddar upp...(%s%)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr "Använd det här för min kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr "Använd som standard"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "Användare"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr "Användar Fel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr "Verifieringsfel"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Values"
msgstr "Värden"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#: code:addons/web/static/src/views/form/form_label.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr "Värden som anges här är företagsspecifika."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr "Variation"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr "Vy %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr "Visa åtkomsträttigheter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "Visa fält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "View In Kanban"
msgstr "Översikt i Kanban"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "View Metadata"
msgstr "Visa metadata"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr "Se Post Regler"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "Typ av vy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr "Omkopplare för vy"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Warning"
msgstr "Varning"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr "Benchmarks för webben"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "Web Mobil Test"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr "Web Tester"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr "Webbplatslänk"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Wed"
msgstr "Ons"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Week"
msgstr "Vecka"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "Well Done!"
msgstr "Duktigt!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Widget:"
msgstr "Grafisk komponent:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
#, python-format
msgid "Would you like to save your changes?"
msgstr "Vill du spara dina ändringar?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/iframe_wrapper/iframe_wrapper_field.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr "Packa in rå html i en iframe"

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
#, python-format
msgid "Wrong login/password"
msgstr "Fel användare/lösenord"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Year"
msgstr "År"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr "Gul"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Yes"
msgstr "Ja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Yesterday"
msgstr "Igår"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_successful
msgid "You are logged in."
msgstr "Du är inloggad."

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "Du har inte tillåtelse att ladda upp en bilaga här."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr "Du kan inte följa relationer för denna fältkedjekonstruktion"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "You do not have access to the model \"%s\"."
msgstr "Du har inte åtkomst till modellen \"%s\"."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr "Du kanske inte tror det,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "Du kanske inte tror det, <br />men applikationen laddar faktiskt..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "You need to be able to edit parent first to add property tags"
msgstr ""
"Du måste kunna redigera föräldern först för att lägga till egenskapstaggar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "You need to be able to edit parent first to configure property fields"
msgstr ""
"Du måste kunna redigera föräldern först för att konfigurera egenskapsfält"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""
"Du måste spara denna nya post innan du redigerar översättningen. Vill du "
"fortsätta?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""
"Du måste starta Odoo med minst två arbetare för att skriva ut en pdf "
"-version av rapporterna."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""
"Du bör uppgradera din version av Wkhtmltopdf till minst 0.12.0 för att få en"
" korrekt visning av sidhuvuden och sidfötter samt stöd för tabellbrytning "
"mellan sidor."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr ""
"Din Odoo -session har löpt ut. Den aktuella sidan kommer att uppdateras."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""
"Din installation av Wkhtmltopdf verkar vara trasig. Rapporten kommer att "
"visas i html."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr "[Ingen widget %s]"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "en dag sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "ungefär en minut sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "ungefär en månad sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "ungefär ett år sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "ungefär en timme sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "all"
msgstr "alla"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "alla poster"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#, python-format
msgid "and"
msgstr "och"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "any"
msgstr "någon"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "are valid for this update."
msgstr "är giltig för denna uppdatering."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr "som en ny"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr "vid:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr "eftersom det laddas..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr "men applikationen laddas faktiskt..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "barn av"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "innehåller"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr "datum"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "innehåller inte"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "innehåller inte"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr "laddar ner..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr "t.ex. Global Business Solutions"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr "för:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "större än"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr "större än eller lika med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr "hex"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "hour"
msgstr "timme"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "hours"
msgstr "timmar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "i"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "är"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is No"
msgstr "är Nej"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is Yes"
msgstr "är Ja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr "är efter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr "är efter eller lika med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr "är före"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr "är före eller lika med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr "är mellan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "är lika med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "är inte"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "är inte ="

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "är inte lika med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr "är inte inställt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr "är inställt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr "laddar ännu..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/utils/numbers.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "mindre än"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "mindre än en minute sedan"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr "mindre än eller lika med"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "like"
msgstr "som"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "menus"
msgstr "menyer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "minute"
msgstr "minut"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "minutes"
msgstr "minuter"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "more"
msgstr "mer"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "new tab"
msgstr "ny flik"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr "ingen beskrivning given"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "none"
msgstr "ingen"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr "inte"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "inte i"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "not like"
msgstr "inte som"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "inte satt (falskt)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "av dessa sökord:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "av:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "on any screen to show shortcut overlays and"
msgstr "på valfri skärm för att visa genvägsöverlägg och"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "eller"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "förälder av"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr "fråga"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "record(s)"
msgstr "post(er)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "records ?"
msgstr "poster ?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "remaining)"
msgstr "återstående)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr "sök"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "selected"
msgstr "valda"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "selected records,"
msgstr "valda poster,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr "satt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "satt (sant)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "to trigger a shortcut."
msgstr "för att aktivera en genväg."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "type a default text or press ENTER"
msgstr "ange en standardtext eller tryck på ENTER"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "— press"
msgstr "- tryck"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#, python-format
msgid "— search for"
msgstr "— leta efter"
