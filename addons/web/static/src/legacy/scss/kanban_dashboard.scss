
.o_legacy_kanban_view.o_kanban_dashboard {
    &:not(.o_kanban_grouped) {
        // correctly display the no_content_helper in dashboards
        flex-flow: row wrap;
    }

    .o_kanban_record {
        position: relative;
        display: flex;
        flex-flow: column nowrap;
        justify-content: space-between;
        padding: $o-kanban-dashboard-vpadding $o-kanban-dashboard-hpadding;

        @include media-breakpoint-down(md) {
            margin-bottom: 10px;
        }

        // ------- Generic layout adaptations -------
        .container {
            width: 100%;
            max-width: none;
        }

        // ------- Dropdown toggle & menu -------
        $o-kanban-manage-toggle-height: 35px;

        .o_kanban_manage_toggle_button {
            @include o-kanban-dropdown($o-kanban-dashboard-hpadding);
            @include o-position-absolute(0, 0);
            height: $o-kanban-manage-toggle-height;
        }

        .o_kanban_card_manage_pane {
            // Arbitrary value to place the dropdown-menu exactly below the
            // dropdown-toggle (height is forced so that it works on Firefox)
            top: $o-kanban-manage-toggle-height;

            > div:not(.o_no_padding_kanban_colorpicker) {
                padding: 3px 0 3px 20px;
                visibility: visible;
                margin-bottom: 5px;
            }

            > .o_kanban_card_manage_section {
                margin-bottom: 10px;

                + .o_kanban_card_manage_section {
                    border-top: 1px solid map-get($grays, '200');
                }

                > div {
                    @include o-kanban-dashboard-dropdown-link;
                }
            }

            // Dropdown menu with complex layout
            &.container {
                width: 95%;
                max-width: 400px;

                .row {
                    display: flex;
                    flex-flow: row nowrap;
                    justify-content: space-between;
                    margin-left: 0;
                    margin-right: 0;
                    padding-left: $o-kanban-dashboard-dropdown-complex-gap*2;
                    padding-right: $o-kanban-dashboard-dropdown-complex-gap*2;
                }

                div[class*="col-"] {
                    flex: 1 1 percentage(1/3);
                    padding-left: $o-kanban-dashboard-dropdown-complex-gap;
                    padding-right: $o-kanban-dashboard-dropdown-complex-gap;
                    max-width: none;

                    > .o_kanban_card_manage_title {
                        @extend .h5;
                        margin: (($font-size-base * $line-height-base) / 2) 0;
                    }
                    > div:not(.o_kanban_card_manage_title) {
                        @include o-kanban-dashboard-dropdown-link($link-padding-gap: $o-kanban-dashboard-dropdown-complex-gap);
                    }
                }

                .row.o_kanban_card_manage_settings {
                    padding-top: $o-kanban-dashboard-dropdown-complex-gap*3;

                    &:not(:first-child) {
                        border-top: 1px solid map-get($grays, '300');
                    }

                    .oe_kanban_colorpicker {
                        max-width: none;
                        padding: 0;
                    }

                    div[class*="col-"] + div[class*="col-"] {
                        border-left: 1px solid map-get($grays, '300');
                    }

                    // Default options box
                    div.text-end {
                        text-align: left; // :/
                        @include o-kanban-dashboard-dropdown-link(0);
                    }
                }
            }

        }

        &.o_dropdown_open {
            .o_kanban_card_manage_pane {
                display: block;
            }
            .o_kanban_manage_toggle_button {
                @include o-kanban-dropdown-open;
                position: absolute;
            }
        }

        // -------  Kanban Record Titles -------
        // Uniform design across different HTML layouts

        // Provide enough room for the dropdown-toggle
        .o_primary {
            padding-right: $o-kanban-dashboard-hpadding*2;
        }

        // Uniform titles
        .o_kanban_card_header_title .o_primary,
        .o_kanban_primary_left .o_primary > span:first-child,
        .oe_kanban_content > .o_title > h3 {
            @include o-kanban-record-title($font-size: 16px);
            display: block;
        }

        // Identify subtitles without classes
        .o_kanban_primary_left .o_primary > span:nth-child(2) > strong {
            font-weight: 500;
            font-size: $font-size-sm;
            color: $text-muted;
        }

        // Provide enough room to add an icon before the title
        &.o_has_icon .o_primary {
            padding-left: $o-kanban-record-margin*1.5;
        }

        // -------  Kanban Content -------
        .o_kanban_card_content {
            display: inline-block;
            vertical-align: top;
            min-height: 80px;
        }

        .o_kanban_card_header + .container.o_kanban_card_content {
            flex: 1 0 auto;
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-between;
            margin-top: $o-kanban-dashboard-vpadding * 2;
            padding-right: 0;
            padding-left: 0;

            &::before, &::after {
                content: normal; // so that space-between works
            }

            a {
                position: relative;
                @include o-text-overflow(inline-block);
            }

            @include media-breakpoint-down(lg) {
                button + a {
                    display: block;
                    margin-top: map-get($spacers, 3);
                }
            }

            .o_kanban_primary_bottom {
                margin-top: $o-kanban-dashboard-vpadding;
                margin-bottom: -$o-kanban-dashboard-vpadding;

                &.bottom_block {
                    border-top: 1px solid map-get($grays, '300');
                    background-color: map-get($grays, '200');
                    padding-top: $o-kanban-dashboard-vpadding;
                    padding-bottom: $o-kanban-dashboard-vpadding;
                }
            }
        }

        .o_dashboard_graph {
            overflow: hidden;
        }
    }

    .o_favorite {
        @include o-position-absolute(3px, $left: 0);
        padding: $o-kanban-record-margin;
    }

    // Emphasize records' colors when necessary
    &.o_emphasize_colors .o_kanban_record::after {
        width: $o-kanban-color-border-width * 2;
    }
}
