# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <j<PERSON><PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2024
# Will Sensors, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Armī<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Original)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Suggested)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "1x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "2 columns"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "2x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "3 Stars"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "3 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "3x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "4 columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "4x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "5 Stars"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "5x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">deg</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2 ms-3\">Y</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2\">X</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Blocks</span>"
msgstr " <span>Bloki</span> "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Customize</span>"
msgstr " <span>Pielāgot</span> "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Above"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Accepts"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Add"
msgstr "Pievienot"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Column"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Row"
msgstr "Pievienot rindu"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Add URL"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a blockquote section."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a button."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a code section."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a link."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row above"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row below"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "Alert"
msgstr "Brīdinājums"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "Izlīdzināšana"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "All"
msgstr "Visi"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "All SCSS Files"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#, python-format
msgid "All documents have been loaded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "All images have been loaded"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Animated"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Anonymous"
msgstr "Anonīms"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Apply"
msgstr "Pielietot"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Are you sure you want to delete the snippet: %s ?"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "Are you sure you want to delete this file ?"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "Pielikums"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "Attachment URL"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Autoplay"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Back to one column."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "Fons"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Background Color"
msgstr "Fona krāsa"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_base
msgid "Base"
msgstr "Bāze"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Basic blocks"
msgstr "Pamata bloki"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Basics"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Below"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Big section heading."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "Bloķēt"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Style"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Width"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Bulleted list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Button"
msgstr "Poga"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Cancel"
msgstr "Atcelt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Careful !"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js:0
#, python-format
msgid "Change media description and tooltip"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Checklist"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Choose a record..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Close"
msgstr "Aizvērt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Code"
msgstr "Kods"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Color"
msgstr "Krāsa"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Color filter"
msgstr "Krāsas filtrs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Colors"
msgstr "Krāsas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Column"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirm"
msgstr "Apstiprināt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirmation"
msgstr "Apstiprināšana"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Content conflict"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Convert into 2 columns."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Convert into 3 columns."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Convert into 4 columns."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy Link"
msgstr "Kopēt saiti"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Could not install module <strong>%s</strong>"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_service.js:0
#, python-format
msgid "Could not load the file \"%s\"."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "Nosegt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Create"
msgstr "Izveidot"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a list with numbering."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a simple bulleted list."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create an URL."
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Crop Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Custom"
msgstr "Pielāgots"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Dailymotion"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dashed"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Default"
msgstr "Noklusētais"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default + Rounded"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Delete"
msgstr "Izdzēst"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Delete %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Delete current table"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Description"
msgstr "Apraksts"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Devices"
msgstr "Iekārtas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Discard"
msgstr "Atmest"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Discard record"
msgstr "Atmet ierakstu"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "Parādīt vārdu"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Do you want to install the %s App?"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Documents"
msgstr "Dokumenti"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dotted"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Double"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Double-click to edit"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Drag and drop the building block."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Dynamic Placeholder"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "ERROR: couldn't get download urls from media library."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Edit Link"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Edit media description"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Youtube Video"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the image in the document."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the youtube video in the document."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Empty quote"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Expected "
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
#, python-format
msgid "File has been uploaded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill"
msgstr "Aizpildīt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill + Rounded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Filter"
msgstr "Filtrs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "Flexible"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Flip"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating shapes"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Floats"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font size"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Format"
msgstr "Formatēt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid ""
"Get the perfect image by searching in our library of copyright free photos "
"and illustrations."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Gradient"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP maršrutēšana"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 2"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 3"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 4"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 5"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 6"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 1"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 2"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 3"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 4"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 5"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 6"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Height"
msgstr "Augstums"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide fullscreen button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide player controls"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide sharing button"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/field_html.js:0
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Html"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon"
msgstr "Ikona"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon Formatting"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 1x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 2x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 3x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 4x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 5x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Icons"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "Illustrations"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Image"
msgstr "Attēls"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Image Formatting"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "Attēla augstums"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "Attēla avots"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "Attēla platums"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Image padding"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Images"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Inline Text"
msgstr "Teksts"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a rating over 3 stars."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a rating over 5 stars."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a table."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a video."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert above"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert an horizontal rule separator."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an image."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert below"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert or edit link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Insert personalized content"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert table"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert your signature."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "Install"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install in progress"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invisible Elements"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Item"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "JS"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "JS file: %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "Large"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz mainīts"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunoja"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunots"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Left"
msgstr "Left"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "Lineāra"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Lines"
msgstr "Rindas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Link"
msgstr "Saite"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "Link copied to clipboard."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Link to"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "List"
msgstr "Saraksts"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "Load more..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Loop"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Marketing Tools"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "Vidējs"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Medium section heading."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "More info about this app."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move down"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move up"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "My Images"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "Nosaukums"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "Navigācija"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No"
msgstr "Numurs"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "No URL specified"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
#, python-format
msgid "No documents found."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "No images found."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No location to drop in"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "No more records"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
#, python-format
msgid "No pictograms found."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "None"
msgstr "Nav"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Normal"
msgstr "Standarta"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Numbered list"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.tests
msgid "Odoo Editor Tests"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Page SCSS Files"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Views"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in a new tab"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Open in new window"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Optimized"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline + Rounded"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Page Options"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paragraph block."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paste as URL"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Position"
msgstr "Amats"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "Priekšskatīt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Primary"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "Izšķirtspēja"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Quote"
msgstr "Piedāvājums"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Readonly field"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove (DELETE)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove Current"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Remove Link"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Remove columns"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current column"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current row"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove format"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove link"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Rename %s"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Replace"
msgstr "Aizstāt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Replace media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Reset"
msgstr "Atstatīt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Reset Size"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Default"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Full"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Half"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Quarter"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Right"
msgstr "Right"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Row"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "SCSS (CSS)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "SCSS file: %s"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Save"
msgstr "Saglabāt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Save and Install"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save and Reload"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Save record"
msgstr "Saglabāt ierakstu"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#, python-format
msgid "Search a document"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.js:0
#, python-format
msgid "Search a pictogram"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Search an image"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Search for a block (e.g. numbers, image wall, ...)"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search for records..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search more..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search to show more records"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Secondary"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Select a block on your page to style it."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Select a media"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Separator"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Server error"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shadow"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Shape"
msgstr "Forma"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Circle"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Rounded"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Thumbnail"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "Show optimized images"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Signature"
msgstr "Signature"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Size"
msgstr "Izmērs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "Small"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Small section heading."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Solid"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"Someone with escalated rights previously modified this area, you are "
"therefore not able to modify it yourself."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Structure"
msgstr "Struktūra"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Style"
msgstr "Stils"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Suggestions"
msgstr "Ieteikumi"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch direction"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch the text's direction."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Table"
msgstr "Tabula"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table Options"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table tools"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Template ID: %s"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Text"
msgstr "Teksts"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Text Color"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text align"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text style"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "The URL seems valid."
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_qweb_fields.py:0
#, python-format
msgid "The datetime %s does not match the format %s"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid ""
"The document was already saved from someone with a different history for "
"model %r, field %r with id %r."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
#, python-format
msgid "The provided url is invalid"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "The provided url is not valid"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"The version from the database will be used.\n"
"                    If you need to keep your changes, copy the content below and edit the new document."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Theme"
msgstr "Tēma"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr "Tēmas krāsas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "There is a conflict between your version and the one in the database."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "This URL is invalid. Preview couldn't be updated."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "This block is outdated"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "This document is not saved!"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "This file is a public view attachment."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "This file is attached to the current record."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "This image is an external image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Title"
msgstr "Nosaukums"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid ""
"To make changes, drop this block and use the new options in the last "
"version."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "To-do"
msgstr "Uzdevumi"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle bold"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle checklist"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle icon spin"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle italic"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle ordered list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle strikethrough"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle underline"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle unordered list"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Tooltip"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Track tasks with a checklist."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""
"Pārveidot attēlu (noklikšķiniet divreiz, lai atiestatītu pārveidošanu)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "Translate"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
#, python-format
msgid "Try searching with other keywords."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Type"
msgstr "Veids"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Type \"/\" for commands"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Unexpected "
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#, python-format
msgid "Upload a document"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Upload an image"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: "
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Video"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Video Formatting"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Video code"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Videos"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "Skatīt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Views and Assets bundles"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Vimeo"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Widgets"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Width"
msgstr "Platums"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Write something..."
msgstr "Rakstīt ..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "XL"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "XML (HTML)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Yes"
msgstr "Jā"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid ""
"You can upload images with the button located in the top left of the screen."
msgstr ""

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Youku"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Your URL"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Youtube"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "and"
msgstr "and"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "default"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "videos"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_websocket
msgid "websocket message handling"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "www.example.com"
msgstr ""
