# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_profile
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# Sarah <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"!<br/>\n"
"            Did not receive it?"
msgstr ""
"!<br/>\n"
"            받지 못하셨습니까?"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "(not verified)"
msgstr "(확인되지 않음)"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
". Collect points on the forum or on the eLearning platform. Those points "
"will make you reach new ranks."
msgstr ". 포럼 또는 온라인 학습 플랫폼의 점수를 수집하세요. 그 점수는 귀하를 새로운 등급으로 안내합니다."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid ". Try another search."
msgstr ". 다른 검색을 시도하십시오."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"fa fa-arrow-right me-1\"/>All Badges"
msgstr ""

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"fa fa-arrow-right\"/> All Badges"
msgstr ""

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"Edit\"/>"
msgstr "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"편집\"/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-1\"/>EDIT"
msgstr "<i class=\"fa fa-pencil me-1\"/>편집"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-2\"/>EDIT PROFILE"
msgstr "<i class=\"fa fa-pencil me-2\"/>프로필 편집"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> 상 받은 사용자 </i>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">국가</option>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold me-2\">Current rank:</small>"
msgstr "<small class=\"fw-bold me-2\">현재 순위:</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Badges</small>"
msgstr "<small class=\"fw-bold\">배지</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Joined</small>"
msgstr "<small class=\"fw-bold\">가입</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Biography</span>"
msgstr "<span class=\"fw-bold\">이력</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">City</span>"
msgstr "<span class=\"fw-bold\">도시</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Country</span>"
msgstr "<span class=\"fw-bold\">국가</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Email</span>"
msgstr "<span class=\"fw-bold\">이메일</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Name</span>"
msgstr "<span class=\"fw-bold\">이름</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Public profile</span>"
msgstr "<span class=\"fw-bold\">공개 프로필</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Website</span>"
msgstr "<span class=\"fw-bold\">웹사이트</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">Badges</span>"
msgstr "<span class=\"text-muted small fw-bold\">배지</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">XP</span>"
msgstr "<span class=\"text-muted small fw-bold\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">Badges</span>"
msgstr "<span class=\"text-muted\">배지</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">XP</span>"
msgstr "<span class=\"text-muted\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"<span id=\"email_validated_message\">Congratulations! Your email has just "
"been validated.</span>"
msgstr "<span id=\"email_validated_message\">축하합니다! 귀하의 이메일을 확인하였습니다.</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "<strong class=\"mb-3 text-white me-2\">Rank by :</strong>"
msgstr ""

#. module: website_profile
#: model:mail.template,body_html:website_profile.validation_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> Profile validation\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hello <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                            You have been invited to validate your email in order to get access to \"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" website.\n"
"                            To validate your email, please click on the following link:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Validate my account\n"
"                                </a>\n"
"                            </div>\n"
"                            Thanks for your participation!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Correct your email address</u>"
msgstr "<u>이메일 주소 수정하기</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Send Again</u>"
msgstr "<u>다시 전송하기</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>here</u>"
msgstr "<u>여기</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "About"
msgstr "소개"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All Users"
msgstr "모든 사용자"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All time"
msgstr "총 시간"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Badges"
msgstr "뱃지"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"                        you receive badges for being especially helpful.<br class=\"d-none d-lg-inline-block\"/>Badges\n"
"                        appear on your profile page, and your posts."
msgstr ""
"질문과 답변으로 평판을 얻는 방법 외에도,\n"
"                        도움을 줌으로써 배지를 획득할 수 있습니다.<br class=\"d-none d-lg-inline-block\"/> 배지는\n"
"                        프로필 페이지와 게시물에서 확인하실 수 있습니다."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Biography"
msgstr "이력"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Clear"
msgstr "제거"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Close"
msgstr "닫기"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit"
msgstr "편집하기"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit Profile"
msgstr "프로필 편집"

#. module: website_profile
#: model:mail.template,name:website_profile.validation_email
msgid "Forum: Email Verification"
msgstr "게시판: 이메일 인증"

#. module: website_profile
#: model:ir.model,name:website_profile.model_gamification_badge
msgid "Gamification Badge"
msgstr "업적 배지"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Home"
msgstr "홈"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I earn badges?"
msgstr "배지는 어떻게 받습니까?"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I score more points?"
msgstr "더 많은 점수를 어떻게 얻습니까?"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__is_published
msgid "Is Published"
msgstr "게시 여부"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Keep learning with"
msgstr "계속 학습"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_website__karma_profile_min
msgid "Minimal karma to see other user's profile"
msgstr "다른 사용자의 프로필을 볼 수 있는 최소 명성"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Mobile sub-nav"
msgstr "모바일 하위 탐색"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "More info"
msgstr "추가 정보"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Nav"
msgstr "탐색"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Next rank:"
msgstr "다음 등급 :"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_main
msgid "No Leaderboard Yet :("
msgstr "현재 획득한 점수가 없습니다. :("

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "No badges yet!"
msgstr "아직 획득한 배지가 없습니다!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid "No user found for"
msgstr "다음에 대한 사용자를 찾을 수 없습니다"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr "답변 또는 의견에 대한 알림을 수신하기 위한 유효한 이메일 주소를 입력하십시오."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Rank badge"
msgstr "배지 등급"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Ranks"
msgstr "등급"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.private_profile
msgid "Return to the website."
msgstr "웹사이트로 돌아갑니다."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search"
msgstr "검색"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search users"
msgstr "사용자 검색"

#. module: website_profile
#: model:mail.template,description:website_profile.validation_email
msgid "Sent to forum visitors to confirm their mail address"
msgstr "게시판 방문자에게 메일 주소 확인을 위해 전송"

#. module: website_profile
#: model:ir.model.fields,help:website_profile.field_gamification_badge__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This month"
msgstr "이번 달"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.private_profile
msgid "This profile is private!"
msgstr "비공개 프로필입니다!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This week"
msgstr "이번 주"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "Unpublished"
msgstr "게시 안 함"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Update"
msgstr "갱신"

#. module: website_profile
#: model:ir.model,name:website_profile.model_res_users
msgid "User"
msgstr "사용자"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "User rank"
msgstr "사용자 순위"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Users"
msgstr "사용자"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Verification Email sent to"
msgstr "다음 주소로 인증 이메일 전송"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_profile
#: model:ir.model,name:website_profile.model_website
msgid "Website"
msgstr "웹사이트"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "When you finish a course or reach milestones, you're awarded badges."
msgstr "과정을 마치거나 이정표에 도달하면 배지가 제공됩니다."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "XP"
msgstr "XP"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
"You can score more points by answering quizzes at the end of each course "
"content. Points can also be earned on the forum. Follow this link to the "
"guidelines of the forum."
msgstr ""
"각 강좌 내용이 끝날 때 퀴즈에 답하면 더 많은 점수를 얻을 수 있습니다. 게시판에서 점수를 얻을 수도 있습니다. 이 링크를 따라 가서 "
"게시판 지침에 따르십시오."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"Your Account has not yet been verified.<br/>\n"
"            Click"
msgstr ""
"계정이 아직 인증되지 않았습니다.<br/>\n"
"            클릭"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Your account does not have an email set up. Please set it up on"
msgstr "계정에 이메일이 설정되어 있지 않습니다. 다음에서 설정하세요."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "breadcrumb"
msgstr "사이트 이동 경로"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "or"
msgstr "또는"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "point"
msgstr "점수"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this month"
msgstr "이번 달"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this week"
msgstr "이번 주"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "to receive a verification email"
msgstr "다음을 클릭하여 인증 이메일을 받습니다"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "xp"
msgstr "xp"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "your account settings"
msgstr "계정 설정"

#. module: website_profile
#: model:mail.template,subject:website_profile.validation_email
msgid "{{ object.company_id.name }} Profile validation"
msgstr "{{ object.company_id.name }} 프로필 검증"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "└ Users"
msgstr "└ 사용자"
