Source: odoo
Section: net
Priority: optional
Maintainer: Odoo S.A. <<EMAIL>>
Uploaders: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
Build-Depends: debhelper-compat (= 12), dh-python, python3, rsync, python3-setuptools
Standards-Version: 4.5.0
Homepage: http://www.odoo.com/
Vcs-Git: https://github.com/odoo/odoo
Vcs-Browser: https://github.com/odoo/odoo

Package: odoo
Architecture: all
Depends:
 ${misc:Depends},
 ${python3:Depends},
 adduser,
# support for multilingual fonts
 fonts-dejavu-core | fonts-freefont-ttf | fonts-freefont-otf | fonts-noto-core,
 fonts-inconsolata,
 fonts-font-awesome,
 fonts-roboto-unhinted,
 gsfonts,
 libjs-underscore,
 lsb-base,
 postgresql-client,
 python3-babel,
 python3-chardet,
 python3-cryptography,
 python3-dateutil,
 python3-decorator,
 python3-docutils,
 python3-freezegun,
 python3-gevent,
 python3-greenlet,
 python3-idna,
 python3-pil,
 python3-jinja2,
 python3-libsass,
# After lxml 5.2, lxml-html-clean is in a separate package
 python3-lxml-html-clean | python3-lxml,
 python3-markupsafe,
 python3-num2words,
 python3-ofxparse,
 python3-passlib,
 python3-polib,
 python3-psutil,
 python3-psycopg2,
 python3-pydot,
 python3-openssl,
 python3-pypdf2,
 python3-qrcode,
 python3-renderpm,
 python3-reportlab,
 python3-requests,
 python3-stdnum,
 python3-tz,
 python3-urllib3,
 python3-vobject,
 python3-werkzeug,
 python3-xlsxwriter,
 python3-xlrd,
 python3-zeep,
Pre-Depends: ${misc:Pre-Depends}
Conflicts: tinyerp-server, openerp-server, openerp-web, openerp
Replaces: tinyerp-server, openerp-server, openerp-web, openerp
Recommends:
 ${python3:Recommends},
 postgresql,
 python3-ldap,
Description: Open Source Apps To Grow Your Business
 Odoo, formerly known as OpenERP, is a suite of open-source business apps
 written in Python and released under the LGPLv3 license. This suite of
 applications covers all business needs, from Website/Ecommerce down to
 manufacturing, inventory and accounting, all seamlessly integrated. Odoo's
 technical features include a distributed server, flexible workflows, an object
 database, a dynamic GUI, customizable reports, and an XML-RPC interface. Odoo
 is the most installed business software in the world. It is used by 2.000.000
 users worldwide ranging from very small companies (1 user) to very large ones
 (300 000 users).
