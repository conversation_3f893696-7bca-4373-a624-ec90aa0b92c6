# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* kb_absent_days_calculate
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20231004\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-05 14:51+0000\n"
"PO-Revision-Date: 2023-12-05 14:51+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: kb_absent_days_calculate
#. odoo-python
#: code:addons/kb_absent_days_calculate/models/hr_payslip.py:0
#, python-format
msgid "Absent Days"
msgstr ""

#. module: kb_absent_days_calculate
#: model:ir.model,name:kb_absent_days_calculate.model_hr_attendance
msgid "Attendance"
msgstr "الحاضرين "

#. module: kb_absent_days_calculate
#: model:ir.model.fields,field_description:kb_absent_days_calculate.field_hr_attendance__kb_date
msgid "Date"
msgstr "التاريخ"

#. module: kb_absent_days_calculate
#: model:ir.model.fields,field_description:kb_absent_days_calculate.field_hr_payslip__kb_day_work_hours
msgid "Day Work Hours"
msgstr "ساعات العمل"

#. module: kb_absent_days_calculate
#. odoo-python
#: code:addons/kb_absent_days_calculate/models/hr_payslip.py:0
#, python-format
msgid "Normal Working Days paid at 100%"
msgstr ""

#. module: kb_absent_days_calculate
#. odoo-python
#: code:addons/kb_absent_days_calculate/models/hr_payslip.py:0
#, python-format
msgid "Over Time Hours"
msgstr "ساعات الوقت الاضافى"

#. module: kb_absent_days_calculate
#: model:ir.model.fields,field_description:kb_absent_days_calculate.field_hr_payslip__kb_over_time_hours
msgid "OverTime Hours"
msgstr "ساعات الوقت الاضافى"

#. module: kb_absent_days_calculate
#: model:ir.model,name:kb_absent_days_calculate.model_hr_payslip
msgid "Pay Slip"
msgstr "قسيمة المرتب"

#. module: kb_absent_days_calculate
#: model:ir.model,name:kb_absent_days_calculate.model_hr_salary_rule
msgid "Salary Rule"
msgstr "قاعدة مرتبات"

#. module: kb_absent_days_calculate
#. odoo-python
#: code:addons/kb_absent_days_calculate/models/hr_payslip.py:0
#, python-format
msgid "Salary Slip of %s for %s"
msgstr ""

#. module: kb_absent_days_calculate
#: model:ir.model.fields,field_description:kb_absent_days_calculate.field_hr_payslip__kb_month_last_day
msgid "month last Day"
msgstr ""
