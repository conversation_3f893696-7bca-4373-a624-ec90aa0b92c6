<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- <record id="tax_invoice3" model="ir.actions.report">
        <field name="name">customer invoice - فواتير عملاء</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">kb_account_alsalem.print_tax_invoice3</field>
        <field name="report_file">kb_account_alsalem.print_tax_invoice3</field>
        <field name="print_report_name">'customer invoice - %s' % (object.name).replace('/', '')</field>
        <field name="binding_model_id" ref="model_account_move" />
        <field name="binding_type">report</field>
    </record> -->

    <template id="print_tax_invoice3">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <link href="https://fonts.googleapis.com/css2?family=Tajawal" rel="stylesheet" />
                <div class="page" style="font-family: 'Tajawal', sans-serif;">
                    <div style="text-align: center; direction: rtl;">
                        <h2>فاتورة ضريبية<br />
                            TAX INVOICE <br />
                        </h2>
                    </div>
                    <t t-foreach="docs" t-as="o">
<!--                    &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;-->
<!--                    &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;-->
<!--                    &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;-->
<!--                    &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;-->
<!--                    <img t-att-src="'/report/barcode/?barcode_type=%s&amp;value=%s&amp;width=%s&amp;height=%s'%('QR', o.l10n_sa_qr_code_str, 90, 90)" />-->
                        <table
                            style="border: none; border-collapse: collapse; width: 98%; margin-left: auto; margin-right: auto;direction: ltr; ">
                            <tr>
                                <th colspan="3" style="border:none; text-align: left; padding: 8px;">
                                    INVOICE NO:
                                    <t t-esc="o.name" />
                                </th>
                                <th colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                    رقم الفاتورة:
                                    <t t-esc="o.name" />
                                </th>
                            </tr>
                            <tr>
                                <th colspan="3" style="border: none; text-align: left; padding: 8px;">
                                    DATE\
                                    <t t-esc="o.invoice_date" t-options='{"widget":"date","format":"dd-MM-yyyy"}'/>
                                </th>
                                <th colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                    التاريخ/
                                    <t t-esc="o.invoice_date" t-options='{"widget":"date","format":"dd-MM-yyyy"}' />
                                </th>
                            </tr>
                            <br></br>
                            <tr>
                                <th colspan="3" style="border: none; text-align: left; padding: 8px;">
                                    From:<br></br>
                                    ALSalem Transportation &#38; Tourism Co<br></br>
                                    Saihat 31972<br></br>
                                    Kingdom of Saudi Arabia<br></br>
                                    ALSalem Transportation &#38; Tourism Co VAT: 310037266100003
                                </th>
                                <th colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                    من:<br></br>
                                    شركة السالم للنقل والسياحة<br></br>
                                    صندوق البريد 31972<br></br>
                                    المملكة العربية السعودية<br></br>
                                    رقم التعريف الضريبي لشركة السالم للنقل والسياحة :310037266100003
                                </th>
                            </tr>
                            <tr>
                                <th colspan="3" style="border: none; text-align: left; padding: 8px;">
                                    To:<br></br>
                                    Address:<br></br>
                                    Building No.
                                    <t t-esc="o.partner_id.building_no" /><br></br>
                                    Additional Building No.
                                    <t t-esc="o.partner_id.additional_building_no" /><br></br>
                                    Posatal Code:
                                    <t t-esc="o.partner_id.zip" /><br></br>
                                    City:
                                    <t t-esc="o.partner_id.city" /><br></br>
                                    State:
                                    <t t-esc="o.partner_id.state_id.name" /><br></br>
                                    <t t-esc="o.partner_id.country_id.name" /><br></br>
                                    Customer VAT Number:
                                    <t t-esc="o.partner_id.vat" /><br></br>
                                    CR Number:
                                    <t t-esc="o.partner_id.kb_company_cr" /><br></br>
                                    Contract#
                                    <t t-esc="o.contract_num" /><br></br>
                                    Invoice Date:
                                    <t t-esc="o.invoice_date" t-options='{"widget":"date","format":"dd-MM-yyyy"}' /><br></br>
                                    Date of Supply:
                                    <t t-esc="o.remarks_note_from" /> -
                                    <t t-esc="o.remarks_note_to" /><br></br>
                                    PO#
                                    <t t-esc="o.po_number" />
                                </th>
                                <th colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                    إلى:<br></br>
                                    العنوان:<br></br>
                                    رقم المبنى:
                                    <t t-esc="o.partner_id.building_no" /><br></br>
                                    الرقم الاضافي:
                                    <t t-esc="o.partner_id.additional_building_no" /><br></br>
                                    الرمز البريدي:
                                    <t t-esc="o.partner_id.zip" /><br></br>
                                    المدينة :
                                    <t t-esc="o.partner_id.city_ar" /><br></br>
                                    المنطقة :
                                    <t t-esc="o.partner_id.state_ar" /><br></br>
                                    <t t-esc="o.partner_id.country_ar" /><br></br>
                                    رقم التعريف الضريبي للعميل :
                                    <t t-esc="o.partner_id.vat" /><br></br>
                                    رقم السجل التجاري:
                                    <t t-esc="o.partner_id.kb_company_cr" /><br></br>
                                    رقم العقد :
                                    <t t-esc="o.contract_num" /><br></br>
                                    تاريخ الفاتورة :
                                    <t t-esc="o.invoice_date" t-options='{"widget":"date","format":"dd-MM-yyyy"}'/><br></br>
                                    تاريخ تقديم الخدمة :
                                    <t t-esc="o.remarks_note_from" /> -
                                    <t t-esc="o.remarks_note_to" /><br></br>
                                    رقم امر الشراء :
                                    <t t-esc="o.po_number" />
                                </th>
                            </tr>
                        </table>
                    </t>
                    <table style="border: none; width: 35%; height: 10%; margin-left: 550px;">
                        <tr>
                            <th colspan="3" style="border:none; text-align: right; padding: 8px;">
                                Billing Currency: SAR
                            </th>
                        </tr>
                    </table>
                    <t t-foreach="docs" t-as="o">
                        <table style="border: 1px solid black; width: 100%">
                            <tr>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 40%; font-size: 10px;">
                                    PO# رقم طلب الشراء
                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 10%; font-size: 10px;">
                                    رقم الاستلام <br></br> RN
                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 20%; font-size: 10px;">
                                    تاريخ الخدمة <br></br> Service Date
                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 10%; font-size: 10px;">
                                    شروط البيع <br></br> Payment
                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 10%; font-size: 10px;">
                                    مذكرة تسليم <br></br> Delivery Note
                                </td>
                            </tr>
                            <tr>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 40%; font-size: 10px;">
                                    <t t-esc="o.po_number" />
                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 10%; font-size: 10px;">

                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 20%; font-size: 10px;">
                                    <t t-esc="o.remarks_note_from" /> - <t t-esc="o.remarks_note_to" />
                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 10%; font-size: 10px;">

                                </td>
                                <td
                                    style="border: 1px solid black; text-align: center; padding: 8px; width: 10%; font-size: 10px;">
                                    <t t-esc="o.delivery_note" />
                                </td>
                            </tr>
                        </table>
                    </t>
                    <br></br>
                    <!-- here is the vat table start -->
                    <table style="border: 1px solid black; width: 100%; direction: ltr;">
                        <tbody class="invoice_tbody">
                            <tr>
                                <td style="text-align:center; border: 1px solid black;">
                                    # <br></br>
                                    Ser
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 20%;">
                                    الوصف <br></br>
                                    Description
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                     تاريخ الخدمة<br></br>
                                     Service Date
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    الكمية <br></br>
                                    Qty
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    الوحدة <br></br>
                                    Unit
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    سعر الوحدة<br></br>
                                    Unit Price
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                     الاجمالي<br></br>
                                     Total 
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    الخصم<br></br>
                                    Discount 
                               </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    الاجمالي قبل الضريبة<br></br>
                                    Total Price excl. VAT
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    نسبة الضريبة <br></br>
                                    %VAT
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                    المبلغ الضريبي<br></br>
                                    VAT Amount
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 10%;">
                                     المجموع<br></br>
                                     Total
                                </td>
                            </tr>
                            <t t-set="current_subtotal" t-value="0" />
                            <t t-set="lines"
                                t-value="o.invoice_line_ids.sorted(key=lambda l: (-l.sequence, l.date, l.move_name, -l.id), reverse=True)" />
                            <t t-foreach="lines" t-as="line">
                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"
                                    groups="account.group_show_line_subtotals_tax_excluded" />
                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_total"
                                    groups="account.group_show_line_subtotals_tax_included" />

                                <tr
                                    t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type" name="account_invoice_line_accountable">
                                        <td style="text-align:center; border: 1px solid black;">
                                            <span t-field="line.sr_no" />
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;" name="account_invoice_line_name"><span t-field="line.name"
                                                t-options="{'widget': 'text'}" /></td>
                                        <td style="text-align:center; border: 1px solid black;"><span t-field="line.service_date" t-options='{"widget":"date","format":"dd-MM-yyyy"}' /></td>
                                        <td style="text-align:center; border: 1px solid black;" class="text-center">
                                            <span t-field="line.quantity" />
                                        </td>   
                                        <td style="text-align:center; border: 1px solid black;" class="text-center">
                                            <span t-field="line.product_uom_id" groups="uom.group_uom" />
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;"
                                            t-attf-class="text-center {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                            <span class="text-nowrap" t-field="line.price_unit" />
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;"
                                        t-attf-class="text-center {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                        <span class="text-nowrap" t-esc="line.quantity * line.price_unit"/>
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;">
                                            <span class="text-nowrap" t-field="line.discount" />
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;" class="text-center o_price_total">
                                            <span class="text-nowrap" t-field="line.price_subtotal"
                                                groups="account.group_show_line_subtotals_tax_excluded" />
                                            <span class="text-nowrap" t-field="line.price_total"
                                                groups="account.group_show_line_subtotals_tax_included" />
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;"
                                            t-attf-class="text-center {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                            <span
                                                t-esc="', '.join(map(lambda x: (x.description or x.name), line.tax_ids))"
                                                id="line_tax_ids" />
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;">
                                            <span class="text-nowrap" t-esc="line.price_total - line.price_subtotal"/>
                                        </td>
                                        <td style="text-align:center; border: 1px solid black;">
                                            <span class="text-nowrap" t-field="line.price_total"/>
                                        </td>
                                    </t>
                                    <t t-if="line.display_type == 'line_section'">
                                        <td style="text-align:center; border: 1px solid black;" colspan="99">
                                            <span t-field="line.name" t-options="{'widget': 'text'}" />
                                        </td>
                                        <t t-set="current_section" t-value="line" />
                                        <t t-set="current_subtotal" t-value="0" />
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td style="text-align:center; border: 1px solid black;" colspan="99">
                                            <span t-field="line.name" t-options="{'widget': 'text'}" />
                                        </td>
                                    </t>
                                </tr>
                                <t
                                    t-if="current_section and (line_last or lines[line_index+1].display_type == 'line_section')">
                                    <tr class="is-subtotal text-right">
                                        <td style="text-align:center; border: 1px solid black;" colspan="99">
                                            <strong class="mr16">Subtotal</strong>
                                            <span t-esc="current_subtotal"
                                                t-options="{&quot;widget&quot;: &quot;monetary&quot;, &quot;display_currency&quot;: o.currency_id}" />
                                        </td>
                                    </tr>
                                </t>
                            </t>
                            <tr>
                                <td colspan="6" style="text-align:center; border: 1px solid black;">
                                    ريال <br></br>
                                    Riyals
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <span class="text-nowrap" t-esc="o.amount_untaxed + o.total_discount"/>
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.total_discount" />
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.amount_untaxed" />
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.amount_tax" />
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.amount_total" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <!-- end here -->
                    <br></br>
                     <table style="border: none; width: 50%; height: 10%; float: right;">
                        <tr>
                            <td colspan="2" style="text-align: center; border: 1px solid black;">
                                Total - الاجمالي
                            </td>
                            <td style="text-align: center; border: 1px solid black;">
                                <span class="text-nowrap" t-esc="o.amount_untaxed + o.total_discount"/>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center; border: 1px solid black;">
                                توقفات للحافلات
                            </td>
                            <td style="text-align: center; border: 1px solid black;">
                                Discount - الخصم
                            </td>
                            <td style="text-align: center; border: 1px solid black;">
                                <t t-esc="o.total_discount" />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center; border: 1px solid black;">
                                Total Excl VAT - الاجمالي قبل الضريبة المضافة
                            </td>
                            <td style="text-align: center; border: 1px solid black;">
                                <t t-esc="o.amount_untaxed" />
                            </td>
                        </tr>
                        <tr>
                                <td colspan="2" style="text-align:center; border: 1px solid black;">
                                     Retention SAR :\  مبلغ محتجز <t t-esc="o.x_dedication"/> %  من الفاتورة  
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                     <t t-esc="o.lessRetention" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                </td>
                            </tr>
                        <tr>
                            <td colspan="2" style="text-align: center; border: 1px solid black;">
                                VAT Amount - الاجمالي للضريبة المضافة
                            </td>
                            <td style="text-align: center; border: 1px solid black;">
                                <t t-esc="o.amount_tax" />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center; border: 1px solid black;">
                                Net Amount - الاجمالي
                            </td>
                            <td style="text-align: center; border: 1px solid black;">
                                <t t-esc="o.amount_total" />
                            </td>
                        </tr>
                    </table>
                    <table style="border: 1px solid black; width: 100%; height: 10%; float: right; direction: rtl;">
                        <tr>
                            <th colspan="3" style="border: 1px solid black; text-align: right; padding: 8px;">
                            <h4 style=" text-align: center;"> Account Numbers</h4> 
                            </th>
                        </tr>
                        <tr>
                             <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                 Bank Name
                            </th>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                IBAN
                            </th>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                 <!--<p t-field="liness.bankimage1" style="font-size: 20px;"/> -->
                            </th>
                        </tr>
                         <t t-foreach="o.partner_id.bankditel2" t-as="liness">
                        <tr>
                             <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                 <p t-field="liness.bankName1" style="font-size: 20px;"/> 
                            </th>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                 <p t-field="liness.IBN1" style="font-size: 20px;"/> 
                            </th>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                 <p t-field="liness.bankimage1" style="font-size: 20px;"/> 
                            </th>
                        </tr>
                    </t>
                  </table>

                   
                    <table
                        style="border: none; border-collapse: collapse; width: 98%; margin-left: auto; margin-right: auto; ">
                        <tr>
                            <td colspan="3" style="border:none; text-align: left; padding: 8px;">
                                &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; <span
                                    style="text-decoration: underline;">FINANCE MANAGER</span>
                            </td>
                            <td colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; <span
                                    style="text-decoration: underline;">المدير المالي</span>
                            </td>
                        </tr>
                    </table>
                </div>
                <!-- <div class="footer" style="font-family: 'Tajawal', sans-serif;">
                    <div t-if="report_type == 'pdf'" style="font-family: 'Tajawal', sans-serif; text-align:center;">
                        Page: <span class="page" /> / <span class="topage" />
                    </div>
                </div> -->
            </t>
        </t>
    </template>
</odoo>