<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>


        <template id="account_report_2">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <link href="https://fonts.googleapis.com/css2?family=Almarai" rel="stylesheet" />
                    <div class="page" style="font-family: 'Almarai', sans-serif;">
                        <br />
                        <br />
                        <style>
                            #table_css {
                                border: 1px solid black border-collapse: collapse;
                                border-spacing: 0px;
                                border-top-spacing: 0px;
                            }

                            #table_css td,
                            #table_css th {
                                border: 1px solid black
                            }

                            #table_css tr: {
                                border: 1px solid black
                            }

                            #table_css th {
                                style="border: 1px solid black"
                            }
                        </style>
                        <h3>Partner Balance Report</h3>
                        <h4> Date: <span t-esc="form_data['partner_date']" /></h4> 
                        <table class="table table-sm o_main_table" id="table_css">
                            <thead>
                                <tr>
                                    <th style="background-color: lightgrey; ">No.</th>
                                    <th style="background-color: lightgrey;width:40%;">Partner Name</th>
                                    <th style="background-color: lightgrey;">Debit</th>
                                    <th style="background-color: lightgrey;">Credit</th>
                                    <th style="background-color: lightgrey;">Balance</th>
                                    <!-- <th style="background-color: lightgrey; width:10%;">Date</th> -->
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="seq" t-value="1" />
                                <t t-set="total_debit" t-value="0" />
                                <t t-set="total_credit" t-value="0" />
                                <t t-set="total_balance" t-value="0" />
                                <t t-foreach="out_list" t-as="line">
                                    <tr>
                                        <td>
                                            <span t-esc="seq" />
                                            <t t-set="seq" t-value="seq + 1" />
                                        </td>
                                        <td>
                                            <span t-esc="line['name']" />
                                        </td>
                                        <td>
                                            <span t-esc="line['credit']" />
                                            <t t-set="total_credit" t-value="total_credit + line['credit']" />
                                        </td>
                                        <td>
                                           <span t-esc="line['debit']" />
                                            <t t-set="total_debit" t-value="total_debit + line['debit']" />
                                        </td>
                                        
                                        <td>
                                           <span t-esc="line['balance']" />
                                            <t t-set="total_balance" t-value="total_balance + line['balance']" />
                                        </td>
                                        <!-- <td> -->
                                           <!-- <span t-esc="line['date']" /> -->
                                        <!-- </td> -->
                                    </tr>
                                </t>
                                <tr>
                                    <td colspan="3" style="text-align:center;"> Total </td>
                                    <td><span t-esc="total_credit" t-options='{"widget": "float", "precision": 2}'/></td>
                                    <td><span t-esc="total_debit" t-options='{"widget": "float", "precision": 2}'/></td>
                                    
                                    <td><span t-esc="total_balance" t-options='{"widget": "float", "precision": 2}'/></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                        <br />


                        <table style="border:none; width:100%; background-color:white;">
                            <tr style="border:none;">
                                <td colspan="6" style="border:none; text-align:center; background-color:white;">
                                    <h4>End of document</h4>
                                    <b style="text-align:center; border:none;">Powerd by knowledge bonds</b>
                                    <br />
                                    <span style="text-align:center; border:none;">www.knowledge-bonds.com</span>
                                </td>
                            </tr>
                        </table>

                    </div>
                </t>
            </t>
        </template>

    </data>
</odoo>