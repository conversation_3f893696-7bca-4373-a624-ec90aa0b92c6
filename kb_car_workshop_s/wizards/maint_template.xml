<?xml version="1.0" encoding="UTF-8"?>
<odoo >
    <record id = "car_work_shop_maint_report_print" model = "ir.actions.report" >
        <field name = "name" >Car workshop Reports</field >
        <field name = "model" >carshop_report_wizard</field >
        <field name = "report_type" >qweb-pdf</field >
        <field name = "report_file" >kb_car_workshop_s.car_workshop_templates_2</field >
        <field name = "report_name" >kb_car_workshop_s.car_workshop_templates_2</field >
        <field name = "binding_model_id" ref = "model_carshop_report_wizard" />
        <field name = "binding_type" >report</field >
    </record >


    <template id = "car_workshop_templates_2" >
        <t t-call = "web.html_container" >
            <t t-call = "web.external_layout" >
                <link href = "https://fonts.googleapis.com/css2?family=Almarai" rel = "stylesheet']" />


                <div style = "page-break-after: always;" >
                    <div class = "page" style = "font-family: 'Tajawal', sans-serif;page-break-after: always;" >


                            <h2 style="text-align: center;" > Vehicle</h2>



                        <table style = "width:100%;" >
                            <tr >
                                 <t t-foreach = "vals_results_loop" t-as = "w" >
                                <td rowspan="2" style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                       Operating Code Number: <span t-esc = "w['vehicle_id']" />
                                    </td >
                                 </t>
                             <th style = "border:1px solid black; background-color:#D3D3D3;text-align:center" >
                                    Oil
                                </th >
                                <th style = "border:1px solid black; background-color:#D3D3D3;text-align:center" >
                                    Oil Filter
                                </th >

                                <th style = "border:1px solid black; background-color:#D3D3D3;text-align:center" >
                                    Transmission Oil
                                </th >

                                 <th style = "border:1px solid black; background-color:#D3D3D3;text-align:center" >
                                    Air Filter
                                </th >

                            </tr >

                            <t t-foreach = "vals_results_loop" t-as = "w" >
                                <tr >

                                <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['oil_without_filter']" />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['oil_filter']" />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['transmission_oil']" />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['air_filter']" />
                                    </td >

                                </tr >

                                <tr>
                                    <th style = "border:1px solid black; background-color:#D3D3D3;text-align:center" >
                                    Change Every KM.
                                </th >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_oil']" />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_oil_filter'] " />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_trans']" />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_filter']" />
                                    </td >

                                </tr>

                                <tr>
                                    <th style = "border:1px solid black; background-color:#D3D3D3;text-align:center" >
                                    Difference
                                </th >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_oil'] - w['oil_without_filter']"   />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_oil_filter'] - w['oil_filter']" />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_trans'] - w['transmission_oil'] " />
                                    </td >

                                    <td style = "border:1px solid black;background-color:#F5F5F5;text-align:center" >
                                        <span t-esc = "w['fleet_filter'] - w['air_filter'] " />
                                    </td >
                                </tr>


                            </t >
                        </table >





                    </div >
                </div >
            </t >
        </t >
    </template >
</odoo >

