# -*- coding: utf-8 -*-

# from odoo import models, fields, api


# class kb_employee_allocation(models.Model):
#     _name = 'kb_employee_allocation.kb_employee_allocation'
#     _description = 'kb_employee_allocation.kb_employee_allocation'

#     name = fields.Char()
#     value = fields.Integer()
#     value2 = fields.Float(compute="_value_pc", store=True)
#     description = fields.Text()
#
#     @api.depends('value')
#     def _value_pc(self):
#         for record in self:
#             record.value2 = float(record.value) / 100
