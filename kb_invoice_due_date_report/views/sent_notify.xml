<odoo>
    <data>

        <record id="ir_cron_send_notifications_noti" forcecreate='True' model="ir.cron">
            <field name="name">Send Notify</field>
            <field eval="True" name="active"/>
            <!--            <field name="user_id" ref="base.user_root"/>-->
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="model_id" ref="model_due_2"/>
            <field name="doall" eval="True"/>
            <field name="state">code</field>
            <field name="code">model.send_notifi_scheduler()</field>
        </record>
    </data>
</odoo>