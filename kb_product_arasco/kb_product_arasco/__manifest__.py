{
    'name': 'KB Product Arasco',
    'version': '********.0',
    'summary': 'Add Arasco functionality to KB Request for Sale',
    'description': """
        This module adds functionality to KB Request for Sale to:
        - Create stock pickings for Arasco products
        - Create sale orders for Arasco products
        - Mark products as Arasco products in request lines
    """,
    'category': 'Inventory',
    'author': 'KB',
    'website': '',
    'depends': [
        'base',
        'sale',
        'stock',
        'kb_request_for_Sale',
        'kb_sales_request_update',
        'kb_stock_picking_driver_info',
    ],
    'data': [
        # 'security/ir.model.access.csv',
        'views/kb_request_for_sale_views.xml',
        'views/sale_order_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
}