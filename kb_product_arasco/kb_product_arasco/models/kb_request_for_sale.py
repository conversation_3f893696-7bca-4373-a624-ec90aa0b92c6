from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import datetime



class KbRequestForSales(models.Model):
    _inherit = "kb_request_for_sale"

    def action_create_arasco_picking(self):
        self.ensure_one()
        arasco_lines = self.kb_product_line_ids.filtered(lambda l: l.is_orasco)
        if not arasco_lines:
            raise ValidationError(_("No Arasco products found. Please mark at least one product as Arasco."))
        if not self.kb_vendor_id:
            raise ValidationError(_("Vendor must be set to create Arasco picking."))

        picking_type_id = self.env['stock.picking.type'].search([('code', '=', 'incoming')], limit=1)
        if not picking_type_id:
            raise ValidationError(_("No incoming picking type found."))
        if picking_type_id.default_location_src_id:
            location_id = picking_type_id.default_location_src_id.id
        else:
            location_id = self.env.ref('stock.stock_location_suppliers').id

        if picking_type_id.default_location_dest_id:
            location_dest_id = picking_type_id.default_location_dest_id.id
        else:
            location_dest_id = self.env.ref('stock.stock_location_stock').id
        for line in arasco_lines:
            if not line.kb_product_id_pro:
                raise ValidationError(_("Product is required for line: %s") % line.kb_product_name)
            if line.kb_product_qty <= 0:
                raise ValidationError(_("Quantity must be greater than 0 for product: %s") % line.kb_product_id_pro.name)
            picking_id = self.env['stock.picking'].create({
                'partner_id': self.kb_partner_id.id,
                'owner_id': self.kb_partner_id.id,
                'picking_type_id': picking_type_id.id,
                'location_id': location_id,
                'location_dest_id': location_dest_id,
                'origin': self.kb_sales_ids,
                'scheduled_date': datetime.now(),
                'company_id': self.company_id.id,
                'req_id': self.id,
                'kb_driver_name': self.kb_ref_1_am,
                'kb_phone_no': self.kb_ref_4_am,
                'kb_car_no': self.kb_ref_2_am,
                'kb_ref_1': self.kb_ref_1_am,
                'kb_ref_2': self.kb_ref_2_am,
                'kb_ref_3': self.kb_ref_3_am,
                'kb_ref_4': self.kb_customer_ref_am,
            })
            if picking_id:
                self.env['stock.move'].create({
                    'name': line.kb_product_id_pro.name,
                    'product_id': line.kb_product_id_pro.id,
                    'product_uom_qty': line.kb_product_qty,
                    'product_uom': line.kb_product_id_pro.uom_id.id,
                    'picking_id': picking_id.id,
                    'location_id': location_id,
                    'location_dest_id': location_dest_id,
                    'company_id': self.company_id.id,
                })
        
        # self.kb_stock_picking_id = [(4, picking_id.id)]
        #
        return {
            'type': 'ir.actions.act_window',
            'name': _('Arasco Picking'),
            'res_model': 'stock.picking',
            'res_id': picking_id.id,
            'view_mode': 'form',
            'context': {'create': False}
        }

    def action_create_arasco_sale_order(self):
        """
        Create sale order for Arasco products.
        This function takes data from kb_request_for_sale_line where is_orasco is True,
        and creates sale order lines for these products.
        """
        self.ensure_one()

        # Check if there are any Arasco products
        arasco_lines = self.kb_product_line_ids.filtered(lambda l: l.is_orasco)
        if not arasco_lines:
            raise ValidationError(_("No Arasco products found. Please mark at least one product as Arasco."))

        # Check if partner is set
        if not self.kb_partner_id:
            raise ValidationError(_("Partner must be set to create Arasco sale order."))

        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.kb_partner_id.id,
            'origin': self.kb_sales_ids,
            'date_order': datetime.now(),
            'company_id': self.company_id.id,
            'req_id': self.id,  # Link back to the request
            # Arasco center fields
            'sale_order_center': f'Arasco-{self.kb_sales_ids}',
            'invoice_number_center': '',  # Can be populated later
            'customer_center': self.kb_partner_id.name if self.kb_partner_id else '',
        })

        # Create sale order lines for each Arasco product
        for line in arasco_lines:
            if not line.kb_product_id_pro:
                raise ValidationError(_("Product is required for line: %s") % line.kb_product_name)
            if line.kb_product_qty <= 0:
                raise ValidationError(_("Quantity must be greater than 0 for product: %s") % line.kb_product_id_pro.name)

            self.env['sale.order.line'].create({
                'product_id': line.kb_product_id_pro.id,
                'name': line.kb_product_id_pro.name,
                'product_uom_qty': line.kb_product_qty,
                'product_uom': line.kb_product_id_pro.uom_id.id,
                'price_unit': line.kb_product_price or 0.0,
                'discount': line.kb_product_discount or 0.0,
                'order_id': sale_order.id,
                'tax_id': [(6, 0, line.kb_product_tax.ids)] if line.kb_product_tax else False,
            })

        # Add the sale order to the kb_sale_order_id field if it exists
        if hasattr(self, 'kb_sale_order_id'):
            self.kb_sale_order_id = sale_order.id

        return {
            'type': 'ir.actions.act_window',
            'name': _('Arasco Sale Order'),
            'res_model': 'sale.order',
            'res_id': sale_order.id,
            'view_mode': 'form',
            'context': {'create': False}
        }



class KbRequestForSalesLine(models.Model):
    _inherit = "kb_request_for_sale_line"

    is_orasco = fields.Boolean(
        string="Is Arasco",
        default=False,
        help="Check this box if this product is for Arasco"
    )

