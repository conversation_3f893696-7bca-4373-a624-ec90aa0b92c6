from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import datetime



class SaleOrder(models.Model):
    _inherit = 'sale.order'

    sale_order_center = fields.Char(
        string='Sale Order Center',
        help='Sale order center information for Arasco'
    )
    invoice_number_center = fields.Char(
        string='Invoice Number Center',
        help='Invoice number center information for Arasco'
    )
    customer_center = fields.Char(
        string='Customer Center',
        help='Customer center information for Arasco'
    )
    delivery_order_center = fields.Char(
        string='Delivery Center',
        help='Delivery center information for Arasco'
    )