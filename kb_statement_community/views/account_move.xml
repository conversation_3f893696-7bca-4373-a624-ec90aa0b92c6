<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_invoice_form" model="ir.ui.view">
        <field name="name">account.invoice.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="kb_statement_id" readonly="1" string="Statement"/>
                <field name="kb_statement_name" string="Statement Name" attrs="{'invisible': [('kb_statement_id', '=', False)]}"/>
                <field name="kb_statement_number" string="Statement Number" attrs="{'invisible': [('kb_statement_id', '=', False)]}"/>
                <field name="kb_statement_line_id" invisible="1" readonly="1" string="Statement Line"/>
            </xpath>
        </field>
    </record>
</odoo>