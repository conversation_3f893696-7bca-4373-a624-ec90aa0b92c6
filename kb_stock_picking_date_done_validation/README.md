# Stock Picking Date Done from Scheduled Date

## Overview

This module ensures that the `date_done` field in `stock.picking` records is set to the `scheduled_date` when the `button_validate` method is called, regardless of any other customizations from other modules.

## Problem Solved

In the standard Odoo behavior, when a stock picking is validated, the `date_done` field is set to the current datetime. However, there are business requirements where `date_done` should reflect the planned/scheduled date rather than the actual validation datetime.

This module ensures that `date_done` always reflects the `scheduled_date` of the transfer, providing consistency between planned and actual transfer dates.

## Features

- **Scheduled Date Consistency**: `date_done` is set to the `scheduled_date` value
- **Override Other Modules**: Takes precedence over existing backdate and other customizations
- **Fallback Logic**: Uses current datetime if `scheduled_date` is not set
- **Audit Trail**: Provides logging of when validation occurs
- **Non-Intrusive**: Doesn't modify existing modules, follows clean architecture principles

## Technical Implementation

The module overrides two key methods in the `stock.picking` model:

1. **`button_validate()`**: Ensures `date_done` is set to `scheduled_date` after validation
2. **`_action_done()`**: Ensures `date_done` is set to `scheduled_date` during the validation process

## Dependencies

- `stock` (base inventory module)
- `sh_all_in_one_backdate` (to ensure proper loading order)

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list
3. Install the module from the Apps menu

## Usage

Once installed, the module works automatically. When users click the "Validate" button on stock pickings:

1. The validation process proceeds normally
2. The `date_done` field is automatically set to the `scheduled_date`
3. If `scheduled_date` is not set, it falls back to current datetime
4. This behavior overrides any other customizations

## Logging

The module provides INFO level logging to track when validations occur:

```
Stock Picking WH/OUT/00001: date_done set to 2025-07-03 10:30:45 (from scheduled_date) when button_validate was called
```

## Compatibility

- Odoo 16.0
- Compatible with existing backdate modules
- Works with all stock picking types (incoming, outgoing, internal)

## Support

For issues or customizations, contact the development team.
