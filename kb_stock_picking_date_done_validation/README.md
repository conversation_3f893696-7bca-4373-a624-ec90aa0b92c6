# Stock Picking Date Done Validation

## Overview

This module ensures that the `date_done` field in `stock.picking` records is set to the current datetime when the `button_validate` method is called, regardless of any backdate customizations from other modules.

## Problem Solved

In the standard Odoo behavior, when a stock picking is validated, the `date_done` field is set to the current datetime. However, some backdate modules override this behavior to set `date_done` to historical dates (like `scheduled_date`, `purchase_id.date_approve`, or `sale_id.date_order`).

This module ensures that `date_done` always reflects the actual datetime when the validation occurred, providing accurate audit trails for when transfers were actually processed.

## Features

- **Accurate Validation Timestamps**: `date_done` is set to the exact datetime when `button_validate` is called
- **Override Backdate Modules**: Takes precedence over existing backdate customizations
- **Audit Trail**: Provides logging of when validation occurs
- **Non-Intrusive**: Doesn't modify existing modules, follows clean architecture principles

## Technical Implementation

The module overrides two key methods in the `stock.picking` model:

1. **`button_validate()`**: Captures the validation datetime and ensures it's applied after validation
2. **`_action_done()`**: Ensures `date_done` is set to current datetime during the validation process

## Dependencies

- `stock` (base inventory module)
- `sh_all_in_one_backdate` (to ensure proper loading order)

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list
3. Install the module from the Apps menu

## Usage

Once installed, the module works automatically. When users click the "Validate" button on stock pickings:

1. The validation process proceeds normally
2. The `date_done` field is automatically set to the current datetime
3. This behavior overrides any backdate customizations

## Logging

The module provides INFO level logging to track when validations occur:

```
Stock Picking WH/OUT/00001: date_done set to 2025-07-03 10:30:45 when button_validate was called
```

## Compatibility

- Odoo 16.0
- Compatible with existing backdate modules
- Works with all stock picking types (incoming, outgoing, internal)

## Support

For issues or customizations, contact the development team.
