# -*- coding: utf-8 -*-
{
    'name': 'Stock Picking Date Done Validation Time',
    'version': '********.0',
    'category': 'Inventory/Inventory',
    'summary': 'Set date_done to current datetime when button_validate is called',
    'description': """
Stock Picking Date Done Validation Time
=======================================

This module ensures that the date_done field in stock.picking is set to the
current datetime when the button_validate method is called, overriding any backdate customizations.

Features:
---------
* Sets date_done to current datetime when validation occurs
* Takes precedence over backdate modules
* Maintains proper validation workflow
* Provides accurate audit trail of when validation actually happened

    """,
    'author': 'Custom Development',
    'depends': [
        'stock',
        'sh_all_in_one_backdate',  # Ensure we load after backdate modules
    ],
    'data': [],
    'test': [
        'tests/test_stock_picking_date_done.py',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
