# -*- coding: utf-8 -*-

from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def _action_done(self):
        """
        Override _action_done to ensure date_done is set to current datetime
        when button_validate is called, regardless of any backdate customizations.
        """
        # Call the parent method first
        result = super(StockPicking, self)._action_done()
        
        # Force set date_done to current datetime for all pickings being validated
        # This ensures the date_done reflects when the validation actually occurred
        current_datetime = fields.Datetime.now()
        
        for picking in self:
            if picking.state == 'done':
                # Use sudo() to bypass any potential access restrictions
                # and directly update the date_done field
                picking.sudo().write({'date_done': current_datetime})
                _logger.info(
                    f"Stock Picking {picking.name}: date_done set to {current_datetime} "
                    f"when button_validate was called"
                )
        
        return result

    def button_validate(self):
        """
        Override button_validate to ensure our date_done logic is applied.
        This method is called when the user clicks the Validate button.
        """
        _logger.info(f"button_validate called for pickings: {self.mapped('name')}")
        
        # Store the current datetime when validation starts
        validation_datetime = fields.Datetime.now()
        
        # Call the parent button_validate method
        result = super(StockPicking, self).button_validate()
        
        # After validation, ensure date_done is set to the validation datetime
        # for any pickings that were successfully validated
        for picking in self:
            if picking.state == 'done':
                picking.sudo().write({'date_done': validation_datetime})
                _logger.info(
                    f"Stock Picking {picking.name}: Final date_done set to {validation_datetime} "
                    f"after button_validate completion"
                )
        
        return result
