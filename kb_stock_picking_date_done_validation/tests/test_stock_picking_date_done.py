# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo import fields
from datetime import datetime, timedelta
import time


class TestStockPickingDateDone(TransactionCase):

    def setUp(self):
        super(TestStockPickingDateDone, self).setUp()
        
        # Create test product
        self.product = self.env['product.product'].create({
            'name': 'Test Product',
            'type': 'product',
            'categ_id': self.env.ref('product.product_category_all').id,
        })
        
        # Create test locations
        self.location_stock = self.env.ref('stock.stock_location_stock')
        self.location_customers = self.env.ref('stock.stock_location_customers')
        
        # Create picking type
        self.picking_type = self.env.ref('stock.picking_type_out')

    def test_date_done_set_to_validation_time(self):
        """Test that date_done is set to current datetime when button_validate is called"""

        # Create a specific scheduled date (different from validation time)
        scheduled_date = fields.Datetime.now() + timedelta(hours=2)

        # Create a stock picking with specific scheduled_date
        picking = self.env['stock.picking'].create({
            'picking_type_id': self.picking_type.id,
            'location_id': self.location_stock.id,
            'location_dest_id': self.location_customers.id,
            'scheduled_date': scheduled_date,
        })

        # Create stock move
        move = self.env['stock.move'].create({
            'name': self.product.name,
            'product_id': self.product.id,
            'product_uom_qty': 1.0,
            'product_uom': self.product.uom_id.id,
            'picking_id': picking.id,
            'location_id': self.location_stock.id,
            'location_dest_id': self.location_customers.id,
        })

        # Confirm the picking
        picking.action_confirm()
        picking.action_assign()

        # Set quantity done
        for move_line in picking.move_line_ids:
            move_line.qty_done = move_line.reserved_uom_qty

        # Record time before validation
        time_before_validation = fields.Datetime.now()

        # Validate the picking
        picking.button_validate()

        # Record time after validation
        time_after_validation = fields.Datetime.now()

        # Assertions
        self.assertEqual(picking.state, 'done', "Picking should be in done state")
        self.assertIsNotNone(picking.date_done, "date_done should be set")

        # Check that date_done is between validation times (not scheduled_date)
        self.assertGreaterEqual(
            picking.date_done, time_before_validation,
            "date_done should be >= time when validation started"
        )
        self.assertLessEqual(
            picking.date_done, time_after_validation,
            "date_done should be <= time when validation completed"
        )

        # Check that date_done is NOT the scheduled_date
        self.assertNotEqual(
            picking.date_done, scheduled_date,
            "date_done should NOT equal scheduled_date"
        )

    def test_date_done_ignores_scheduled_date(self):
        """Test that date_done is set to validation time, not scheduled_date, even if scheduled_date is in the past"""

        # Create a picking with scheduled_date in the past
        past_date = fields.Datetime.now() - timedelta(days=5)

        picking = self.env['stock.picking'].create({
            'picking_type_id': self.picking_type.id,
            'location_id': self.location_stock.id,
            'location_dest_id': self.location_customers.id,
            'scheduled_date': past_date,
        })

        # Create stock move
        move = self.env['stock.move'].create({
            'name': self.product.name,
            'product_id': self.product.id,
            'product_uom_qty': 1.0,
            'product_uom': self.product.uom_id.id,
            'picking_id': picking.id,
            'location_id': self.location_stock.id,
            'location_dest_id': self.location_customers.id,
        })

        # Confirm and assign
        picking.action_confirm()
        picking.action_assign()

        # Set quantity done
        for move_line in picking.move_line_ids:
            move_line.qty_done = move_line.reserved_uom_qty

        # Record current time
        current_time = fields.Datetime.now()

        # Validate the picking
        picking.button_validate()

        # Assertions
        self.assertEqual(picking.state, 'done')
        self.assertIsNotNone(picking.date_done)

        # date_done should be close to current time, not the past scheduled_date
        time_diff = abs((picking.date_done - current_time).total_seconds())
        self.assertLess(time_diff, 5, "date_done should be close to validation time")

        # date_done should definitely not be the scheduled_date
        self.assertNotEqual(
            picking.date_done, past_date,
            "date_done should NOT be the scheduled_date"
        )

    def test_date_done_always_current_time(self):
        """Test that date_done is always set to current time regardless of scheduled_date"""

        # Create a picking without scheduled_date
        picking = self.env['stock.picking'].create({
            'picking_type_id': self.picking_type.id,
            'location_id': self.location_stock.id,
            'location_dest_id': self.location_customers.id,
        })

        # Clear scheduled_date to ensure it's None
        picking.write({'scheduled_date': False})

        # Create stock move
        move = self.env['stock.move'].create({
            'name': self.product.name,
            'product_id': self.product.id,
            'product_uom_qty': 1.0,
            'product_uom': self.product.uom_id.id,
            'picking_id': picking.id,
            'location_id': self.location_stock.id,
            'location_dest_id': self.location_customers.id,
        })

        # Confirm and assign
        picking.action_confirm()
        picking.action_assign()

        # Set quantity done
        for move_line in picking.move_line_ids:
            move_line.qty_done = move_line.reserved_uom_qty

        # Record current time
        current_time = fields.Datetime.now()

        # Validate the picking
        picking.button_validate()

        # Assertions
        self.assertEqual(picking.state, 'done')
        self.assertIsNotNone(picking.date_done)

        # date_done should be close to current time
        time_diff = abs((picking.date_done - current_time).total_seconds())
        self.assertLess(time_diff, 5, "date_done should be close to current validation time")
