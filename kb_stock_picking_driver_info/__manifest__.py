{
    'name': 'KB Stock Picking Driver Info',
    'version': '********.0',
    'category': 'Inventory/Inventory',
    'summary': 'Add driver information fields to stock picking',
    'description': """
KB Stock Picking Driver Info
============================
This module adds driver information fields to stock picking:
- Driver Name
- Phone Number  
- Car Number

These fields are populated from KB Request for Sale when creating Arasco pickings.
    """,
    'depends': ['stock', 'kb_request_for_Sale', 'kb_ref_fields'],
    'data': [
        'views/stock_picking_views.xml',
        'views/sale_order.xml',
        'views/purchase_order.xml',
        'views/account_move.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
