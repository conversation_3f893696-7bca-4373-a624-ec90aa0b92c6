from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    kb_ref_3_po = fields.Char(
        string='Identity No.',
    )
    kb_ref_4_po = fields.Char(
        string='Phone No.',
    )
    kb_vendor_ref_am = fields.Char(
        string='Vendor Reference',
    )

    def _prepare_invoice(self, **optional_values):
        res = super(PurchaseOrder, self)._prepare_invoice(**optional_values)
        res.update({
            'kb_ref_3_am': self.kb_ref_3_po,
            'kb_ref_4_am': self.kb_ref_4_po,
            'kb_vendor_ref_am': self.kb_vendor_ref_am,
        })
        return res
