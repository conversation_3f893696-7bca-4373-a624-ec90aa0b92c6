<?xml version="1.0" encoding="utf-8" ?>
<odoo>
	<!--inheriting the pos order report view-->
	<record id="report_pos_order_view_search" model="ir.ui.view">
		<field name="name">report.pos.order.view.search.inherit.multi.branch.pos</field>
		<field name="model">report.pos.order</field>
		<field name="inherit_id"
			   ref="point_of_sale.view_report_pos_order_search"/>
		<field name="arch" type="xml">
			<xpath expr="//search/group" position="inside">
				<filter string="Branch" name="Branch"
						context="{'group_by':'branch_id'}"/>
			</xpath>
		</field>
	</record>
</odoo>
